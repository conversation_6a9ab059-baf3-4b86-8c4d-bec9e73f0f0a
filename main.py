import open3d as o3d
import numpy as np
import pandas as pd
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import matplotlib
from matplotlib import cm
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import time

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# ========== 工具函数 ==========
def load_point_cloud(file_path):
    """加载点云文件"""
    start_time = time.time()
    pcd = o3d.io.read_point_cloud(file_path)
    print(f"加载点云耗时 {time.time() - start_time:.2f} 秒")
    return pcd

def get_point_cloud_color_classes(pcd):
    """分离黄色和蓝色点云"""
    points = np.asarray(pcd.points)
    colors = np.asarray(pcd.colors)
    yellow_mask = np.all(np.abs(colors - [1, 1, 0]) < 0.1, axis=1)
    blue_mask = np.all(np.abs(colors - [0, 0, 1]) < 0.1, axis=1)
    return points[yellow_mask], points[blue_mask]

def compute_normals(pcd, knn=50):
    """计算点云法向量"""
    start_time = time.time()
    pcd.estimate_normals(search_param=o3d.geometry.KDTreeSearchParamKNN(knn=knn))
    normals = np.asarray(pcd.normals)
    print(f"计算法向量耗时 {time.time() - start_time:.2f} 秒")
    return normals

def compute_perpendicular_distance(point_a, point_b, normal):
    """计算点之间的垂直距离"""
    vector_ab = point_a - point_b
    return np.abs(np.dot(vector_ab, normal))

def create_continuous_colormap():
    """创建连续颜色映射"""
    return cm.get_cmap('viridis')

def get_color_for_thickness(thickness, cmap):
    """根据厚度值获取连续颜色，固定范围 [0, 1] mm"""
    fixed_min, fixed_max = 0.0, 1.0
    norm_thickness = (thickness - fixed_min) / (fixed_max - fixed_min) if fixed_max != fixed_min else 0.5
    norm_thickness = np.clip(norm_thickness, 0, 1)
    rgba = cmap(norm_thickness)
    return rgba[:3]

def smooth_thickness(point_thickness, points, region_size=70.0):
    """平滑厚度值以减少区域边界效果"""
    smoothed_thickness = point_thickness.copy()
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    kdtree = o3d.geometry.KDTreeFlann(pcd)
    for i in range(len(points)):
        if not np.isnan(point_thickness[i]):
            _, indices, _ = kdtree.search_radius_vector_3d(points[i], region_size / 2)
            neighbor_thickness = point_thickness[indices]
            valid_thickness = neighbor_thickness[~np.isnan(neighbor_thickness)]
            if len(valid_thickness) > 0:
                smoothed_thickness[i] = np.mean(valid_thickness)
            else:
                smoothed_thickness[i] = point_thickness[i]  # 保留原始值
    return smoothed_thickness

# ========== 优化后的厚度测量和着色函数 ==========
def measure_and_color_thickness(file_path, region_size=70.0, num_samples=10):
    """测量点云厚度并使用连续颜色进行着色（固定厚度范围 [0, 1] mm，增加平滑处理）"""
    start_time = time.time()
    pcd = load_point_cloud(file_path)
    points = np.asarray(pcd.points)

    if not pcd.has_colors():
        pcd.colors = o3d.utility.Vector3dVector(np.ones((len(points), 3)) * 0.5)
    colors = np.asarray(pcd.colors)

    a_points, b_points = get_point_cloud_color_classes(pcd)

    if len(a_points) == 0 or len(b_points) == 0:
        print("未找到有效的A类或B类点")
        return pd.DataFrame(), pcd, 0.0, 1.0, None

    a_pcd = o3d.geometry.PointCloud()
    a_pcd.points = o3d.utility.Vector3dVector(a_points)
    a_kdtree = o3d.geometry.KDTreeFlann(a_pcd)
    a_normals = compute_normals(a_pcd, knn=50)

    b_pcd = o3d.geometry.PointCloud()
    b_pcd.points = o3d.utility.Vector3dVector(b_points)
    b_kdtree = o3d.geometry.KDTreeFlann(b_pcd)
    b_normals = compute_normals(b_pcd, knn=50)

    x_min, x_max = points[:, 0].min(), points[:, 0].max()
    y_min, y_max = points[:, 1].min(), points[:, 1].max()
    x_range = x_max - x_min
    y_range = y_max - y_min

    total_area = x_range * y_range
    print(f"X范围: {x_min:.2f} 至 {x_max:.2f} mm, Y范围: {y_min:.2f} 至 {y_max:.2f} mm, 总面积: {total_area:.2f} mm²")

    ideal_area_per_region = 55000
    target_regions = round(total_area / ideal_area_per_region)

    ideal_region_side = 70.0
    num_x = max(2, round(x_range / ideal_region_side))
    num_y = max(2, round(y_range / ideal_region_side))

    x_step = x_range / num_x
    y_step = y_range / num_y

    min_region_size = 10.0
    max_region_size = 200.0
    if x_step < min_region_size or y_step < min_region_size:
        scale = max(min_region_size / x_step, min_region_size / y_step)
        num_x = max(2, round(num_x / scale))
        num_y = max(2, round(num_y / scale))
        x_step = x_range / num_x
        y_step = y_range / num_y
    elif x_step > max_region_size or y_step > max_region_size:
        scale = min(max_region_size / x_step, max_region_size / y_step)
        num_x = max(2, round(num_x * scale))
        num_y = max(2, round(num_y * scale))
        x_step = x_range / num_x
        y_step = y_range / num_y

    print(f"目标区域数: {target_regions}, num_x: {num_x}, num_y: {num_y}, x_step: {x_step:.2f} mm, y_step: {y_step:.2f} mm")
    print(f"每个区域面积: {x_step * y_step:.2f} mm²")

    results = []
    point_thickness = np.full(len(points), np.nan)
    thickness_values = []

    region_idx = 0
    for i in range(num_x):
        for j in range(num_y):
            region_idx += 1
            region_start_time = time.time()
            x_start = x_min + i * x_step
            x_end = x_start + x_step
            y_start = y_min + j * y_step
            y_end = y_start + y_step

            a_region_mask = (a_points[:, 0] >= x_start) & (a_points[:, 0] <= x_end) & \
                            (a_points[:, 1] >= y_start) & (a_points[:, 1] <= y_end)
            a_region_points = a_points[a_region_mask]
            a_region_indices = np.where(a_region_mask)[0]
            a_region_normals = a_normals[a_region_indices]

            b_region_mask = (b_points[:, 0] >= x_start) & (b_points[:, 0] <= x_end) & \
                            (b_points[:, 1] >= y_start) & (b_points[:, 1] <= y_end)
            b_region_points = b_points[b_region_mask]
            b_region_indices = np.where(b_region_mask)[0]
            b_region_normals = b_normals[b_region_indices]

            print(f"区域 {region_idx}: A类点数: {len(a_region_points)}, B类点数: {len(b_region_points)}")

            if len(a_region_points) < num_samples or len(b_region_points) < num_samples:
                print(f"区域 {region_idx} 因点数不足被跳过")
                continue

            region_center = np.mean(a_region_points, axis=0)
            _, a_sample_indices, _ = a_kdtree.search_knn_vector_3d(region_center, num_samples)
            a_samples = a_points[a_sample_indices]
            a_sample_normals = a_normals[a_sample_indices]

            _, b_sample_indices, _ = b_kdtree.search_knn_vector_3d(region_center, num_samples)
            b_samples = b_points[b_sample_indices]
            b_sample_normals = b_normals[b_sample_indices]

            thicknesses = []
            for idx, point in enumerate(a_samples):
                normal = a_sample_normals[idx]
                _, b_idx, _ = b_kdtree.search_knn_vector_3d(point, 1)
                closest_b = b_points[b_idx[0]]
                thickness = compute_perpendicular_distance(point, closest_b, normal)
                thicknesses.append(thickness)

            for idx, point in enumerate(b_samples):
                normal = b_sample_normals[idx]
                _, a_idx, _ = a_kdtree.search_knn_vector_3d(point, 1)
                closest_a = a_points[a_idx[0]]
                thickness = compute_perpendicular_distance(point, closest_a, normal)
                thicknesses.append(thickness)

            if len(thicknesses) >= 6:
                thicknesses = np.sort(thicknesses)
                thicknesses = thicknesses[2:-2]
                region_thickness = np.mean(thicknesses)
                thickness_values.append(region_thickness)
                results.append({
                    "Region": region_idx,
                    "Thickness": region_thickness,
                    "x_start": x_start,
                    "x_end": x_end,
                    "y_start": y_start,
                    "y_end": y_end
                })

                region_mask = (points[:, 0] >= x_start) & (points[:, 0] <= x_end) & \
                              (points[:, 1] >= y_start) & (points[:, 1] <= y_end)
                yellow_mask = np.all(np.abs(colors - [1, 1, 0]) < 0.1, axis=1)
                blue_mask = np.all(np.abs(colors - [0, 0, 1]) < 0.1, axis=1)

                point_thickness[region_mask & yellow_mask] = region_thickness
                point_thickness[region_mask & blue_mask] = region_thickness

                print(f"区域 {region_idx} 厚度: {region_thickness:.4f} mm")
                print(f"区域 {region_idx} 处理耗时 {time.time() - region_start_time:.2f} 秒")

    # 平滑厚度值
    try:
        point_thickness = smooth_thickness(point_thickness, points, region_size)
    except Exception as e:
        print(f"厚度平滑处理出错: {e}")
        point_thickness = point_thickness  # 保留原始厚度值

    # 准备着色
    cmap = create_continuous_colormap()
    if results:
        df = pd.DataFrame(results)
        min_thickness = 0.0
        max_thickness = 1.0
        new_colors = colors.copy()
        for i, thickness in enumerate(point_thickness):
            if not np.isnan(thickness):
                new_colors[i] = get_color_for_thickness(thickness, cmap)
        pcd.colors = o3d.utility.Vector3dVector(new_colors)
    else:
        df = pd.DataFrame()
        min_thickness = 0.0
        max_thickness = 1.0

    print(f"最终结果: 处理了 {len(results)} 个区域")
    print(f"总处理耗时: {time.time() - start_time:.2f} 秒")
    if not df.empty:
        print(df)

    return df, pcd, min_thickness, max_thickness, cmap

# ========== 交互界面 ==========
def display_table(df, pcd, min_thickness, max_thickness, cmap):
    """显示厚度测量结果和连续颜色映射（固定厚度范围 [0, 1] mm）"""
    start_time = time.time()
    root = tk.Tk()
    root.title("厚度测量结果")
    root.geometry("1000x800")

    try:
        root.option_add("*Font", "SimHei 12")
    except:
        root.option_add("*Font", "Arial 12")

    def on_close():
        root.quit()
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_close)

    if df.empty:
        messagebox.showwarning("警告", "无有效厚度数据")
        root.destroy()
        return

    main_frame = ttk.Frame(root)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    left_frame = ttk.Frame(main_frame)
    left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

    title_label = ttk.Label(left_frame, text="区域厚度测量结果", font=("SimHei", 14, "bold"))
    title_label.pack(side=tk.TOP, pady=5)

    tree = ttk.Treeview(left_frame, columns=("Region", "Thickness"), show="headings",
                        height=min(len(df), 20))
    tree.heading("Region", text="区域索引")
    tree.heading("Thickness", text="平均厚度 (mm)")
    tree.column("Region", width=150, anchor="center")
    tree.column("Thickness", width=150, anchor="center")

    vsb = ttk.Scrollbar(left_frame, orient="vertical", command=tree.yview)
    tree.configure(yscrollcommand=vsb.set)

    for _, row in df.iterrows():
        values = [f"区域 {int(row['Region'])}", f"{row['Thickness']:.4f}"]
        tree.insert("", "end", values=values, tags=(int(row['Region']),))

    tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    vsb.pack(side=tk.RIGHT, fill=tk.Y)

    right_frame = ttk.Frame(main_frame)
    right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)

    fig = Figure(figsize=(6, 8), dpi=100)
    ax1 = fig.add_subplot(211)
    ax2 = fig.add_subplot(212)

    ax1.plot(df["Region"], df["Thickness"], '-o', color='darkblue', markersize=8, linewidth=2)
    ax1.set_title("各区域平均厚度分布", fontsize=14)
    ax1.set_xlabel("区域索引", fontsize=12)
    ax1.set_ylabel("平均厚度 (mm)", fontsize=12)
    ax1.grid(True, linestyle='--', alpha=0.7)
    ax1.set_xticks(df["Region"])
    ax1.set_xticklabels([f"区域 {int(i)}" for i in df["Region"]], rotation=45)

    ax2.set_title('厚度-颜色映射关系 (固定范围 0-1 mm)', fontsize=14)
    ax2.axis('off')

    norm = plt.Normalize(0.0, 1.0)
    sm = plt.cm.ScalarMappable(cmap=cmap, norm=norm)
    cbar = fig.colorbar(sm, ax=ax2, orientation='horizontal', pad=0.2, aspect=30)
    cbar.set_label('厚度 (mm)', fontsize=12)
    cbar.ax.tick_params(labelsize=10)

    ax2.text(0.5, 0.9, "厚度-颜色对应关系 (0-1 mm)", ha='center', va='center', fontsize=12, fontweight='bold')

    canvas = FigureCanvasTkAgg(fig, master=right_frame)
    canvas.draw()
    canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    button_frame = ttk.Frame(root)
    button_frame.pack(fill=tk.X, padx=10, pady=10)

    def show_point_cloud():
        try:
            vis = o3d.visualization.Visualizer()
            vis.create_window(window_name=f"厚度着色点云 (固定厚度范围: 0-1 mm)", width=800, height=600)
            vis.add_geometry(pcd)
            render_option = vis.get_render_option()
            render_option.point_size = 2.0
            vis.add_geometry(o3d.geometry.TriangleMesh.create_coordinate_frame(size=10.0))
            vis.run()
            vis.destroy_window()
        except Exception as e:
            print(f"显示点云出错: {e}")
            messagebox.showerror("错误", f"无法显示点云: {e}")

    def show_region_point_cloud(event):
        start_time = time.time()
        selection = tree.selection()
        if not selection:
            return

        item = tree.item(selection[0])
        region_id = int(item['tags'][0])
        region_data = df[df['Region'] == region_id].iloc[0]
        x_start = region_data['x_start']
        x_end = region_data['x_end']
        y_start = region_data['y_start']
        y_end = region_data['y_end']
        thickness = region_data['Thickness']

        points = np.asarray(pcd.points)
        colors = np.asarray(pcd.colors)
        region_mask = (points[:, 0] >= x_start) & (points[:, 0] <= x_end) & \
                      (points[:, 1] >= y_start) & (points[:, 1] <= y_end)

        if np.sum(region_mask) == 0:
            messagebox.showwarning("警告", f"区域 {region_id} 无点云数据")
            return

        region_pcd = o3d.geometry.PointCloud()
        region_pcd.points = o3d.utility.Vector3dVector(points)
        new_colors = colors.copy()
        new_colors[region_mask] = [1, 0, 0]
        region_pcd.colors = o3d.utility.Vector3dVector(new_colors)

        z_min, z_max = points[:, 2].min(), points[:, 2].max()
        bbox = o3d.geometry.AxisAlignedBoundingBox(
            min_bound=[x_start, y_start, z_min],
            max_bound=[x_end, y_end, z_max]
        )
        bbox.color = (1, 0, 0)

        try:
            vis = o3d.visualization.Visualizer()
            vis.create_window(window_name=f"区域 {region_id} 点云 (厚度: {thickness:.4f}mm)", width=800, height=600)
            vis.add_geometry(region_pcd)
            vis.add_geometry(bbox)
            render_option = vis.get_render_option()
            render_option.point_size = 2.0
            vis.add_geometry(o3d.geometry.TriangleMesh.create_coordinate_frame(size=10.0))
            vis.run()
            vis.destroy_window()
            print(f"区域 {region_id} 可视化耗时 {time.time() - start_time:.2f} 秒")
        except Exception as e:
            print(f"显示区域点云出错: {e}")
            messagebox.showerror("错误", f"无法显示区域点云: {e}")

    tree.bind('<Double-1>', show_region_point_cloud)

    view_button = ttk.Button(button_frame, text="查看3D点云", command=show_point_cloud)
    view_button.pack(side=tk.RIGHT, padx=10, pady=5, ipadx=10, ipady=5)

    def save_point_cloud():
        file_path = filedialog.asksaveasfilename(
            defaultextension=".ply",
            filetypes=[("PLY files", "*.ply"), ("PCD files", "*.pcd")]
        )
        if file_path:
            o3d.io.write_point_cloud(file_path, pcd)
            messagebox.showinfo("保存成功", f"点云已保存至: {file_path}")

    save_button = ttk.Button(button_frame, text="保存点云", command=save_point_cloud)
    save_button.pack(side=tk.RIGHT, padx=10, pady=5, ipadx=10, ipady=5)

    print(f"显示表格设置耗时 {time.time() - start_time:.2f} 秒")
    root.mainloop()

def select_file():
    """选择点云文件"""
    root = tk.Tk()
    root.withdraw()
    return filedialog.askopenfilename(filetypes=[("Point Cloud Files", "*.ply *.pcd")])

# ========== 主函数 ==========
def main():
    """主函数，运行点云厚度测量和可视化"""
    file_path = select_file()
    if not file_path:
        print("未选择文件")
        return

    df, pcd, min_thickness, max_thickness, cmap = measure_and_color_thickness(file_path, region_size=70.0, num_samples=10)

    if df.empty:
        messagebox.showwarning("警告", "无法计算出有效厚度数据")
        return

    display_table(df, pcd, min_thickness, max_thickness, cmap)

if __name__ == "__main__":
    main()
