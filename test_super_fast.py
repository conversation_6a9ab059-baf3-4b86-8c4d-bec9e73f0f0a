"""
测试超快速版本的基本功能
"""
import numpy as np
import time

def test_basic_functions():
    """测试基本函数"""
    print("=== 测试超快速版本基本功能 ===")
    
    try:
        from main_super_fast import get_color_masks_fast, get_color_for_thickness, create_continuous_colormap
        
        # 测试颜色分类
        test_colors = np.array([
            [1.0, 1.0, 0.0],  # 黄色
            [0.0, 0.0, 1.0],  # 蓝色
            [0.5, 0.5, 0.5],  # 灰色
            [0.95, 0.95, 0.05],  # 接近黄色
            [0.05, 0.05, 0.95],  # 接近蓝色
        ], dtype=np.float32)
        
        yellow_indices, blue_indices = get_color_masks_fast(test_colors)
        print(f"✓ 颜色分类测试成功")
        print(f"  黄色点索引: {yellow_indices}")
        print(f"  蓝色点索引: {blue_indices}")
        
        # 测试颜色映射
        cmap = create_continuous_colormap()
        test_thickness = 0.5
        color = get_color_for_thickness(test_thickness, cmap)
        print(f"✓ 颜色映射测试成功，厚度{test_thickness}对应颜色: {color}")
        
        return True
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

def test_performance():
    """测试性能"""
    print("\n=== 测试性能 ===")
    
    try:
        from main_super_fast import get_color_masks_fast
        
        # 创建大量测试数据
        n_points = 100000
        test_colors = np.random.rand(n_points, 3).astype(np.float32)
        # 添加一些黄色和蓝色点
        test_colors[::1000] = [1.0, 1.0, 0.0]  # 黄色
        test_colors[1::1000] = [0.0, 0.0, 1.0]  # 蓝色
        
        start_time = time.time()
        yellow_indices, blue_indices = get_color_masks_fast(test_colors)
        end_time = time.time()
        
        print(f"✓ 处理{n_points}个点耗时: {end_time - start_time:.4f} 秒")
        print(f"  找到黄色点: {len(yellow_indices)}, 蓝色点: {len(blue_indices)}")
        print(f"  处理速度: {n_points/(end_time - start_time):.0f} 点/秒")
        
        return True
    except Exception as e:
        print(f"✗ 性能测试失败: {e}")
        return False

def test_memory_usage():
    """测试内存使用"""
    print("\n=== 测试内存使用 ===")
    
    try:
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        start_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        from main_super_fast import get_color_masks_fast
        
        # 创建大量数据
        n_points = 500000
        test_colors = np.random.rand(n_points, 3).astype(np.float32)
        
        yellow_indices, blue_indices = get_color_masks_fast(test_colors)
        
        end_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_used = end_memory - start_memory
        
        print(f"✓ 处理{n_points}个点的内存使用: {memory_used:.2f} MB")
        print(f"  平均每个点: {memory_used/n_points*1024:.3f} KB")
        
        # 清理
        del test_colors, yellow_indices, blue_indices
        import gc
        gc.collect()
        
        return True
    except Exception as e:
        print(f"✗ 内存测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试超快速版本...")
    
    all_passed = True
    
    if not test_basic_functions():
        all_passed = False
    
    if not test_performance():
        all_passed = False
    
    if not test_memory_usage():
        all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("✓ 所有测试通过！")
        print("\n超快速版本特点:")
        print("- 使用向量化操作，无需复杂编译")
        print("- 智能采样，支持超大文件")
        print("- 多线程处理，平衡速度和内存")
        print("- 大区域划分，减少计算量")
        print("\n现在可以运行 main_super_fast.py 处理您的PLY文件了！")
    else:
        print("✗ 部分测试失败")
    
    print("="*50)

if __name__ == "__main__":
    main()
