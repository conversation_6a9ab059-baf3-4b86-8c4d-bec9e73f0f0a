"""
超高效版本性能测试脚本
"""
import time
import os
import sys
import psutil
import gc
from tkinter import filedialog
import tkinter as tk

def get_memory_usage():
    """获取当前内存使用量（MB）"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024

def test_ultra_optimized_version(file_path):
    """测试超高效版本性能"""
    print("\n=== 测试超高效版本 ===")
    start_time = time.time()
    start_memory = get_memory_usage()
    peak_memory = start_memory
    
    try:
        # 导入超高效版本函数
        from main_ultra_optimized import measure_and_color_thickness_ultra
        
        # 根据文件大小动态调整参数
        file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
        print(f"文件大小: {file_size_mb:.1f} MB")
        
        if file_size_mb > 500:
            max_points = 800000
            region_size = 120.0
            num_samples = 5
        elif file_size_mb > 200:
            max_points = 1200000
            region_size = 100.0
            num_samples = 6
        else:
            max_points = None
            region_size = 80.0
            num_samples = 8
        
        # 监控内存使用
        def monitor_memory():
            nonlocal peak_memory
            while True:
                current_memory = get_memory_usage()
                if current_memory > peak_memory:
                    peak_memory = current_memory
                time.sleep(0.5)
        
        import threading
        monitor_thread = threading.Thread(target=monitor_memory, daemon=True)
        monitor_thread.start()
        
        # 运行超高效版本
        df, pcd, min_thickness, max_thickness, cmap = measure_and_color_thickness_ultra(
            file_path, region_size=region_size, num_samples=num_samples, max_points=max_points
        )
        
        end_time = time.time()
        end_memory = get_memory_usage()
        
        print(f"超高效版本结果:")
        print(f"  处理时间: {end_time - start_time:.2f} 秒")
        print(f"  内存使用: {end_memory - start_memory:.2f} MB")
        print(f"  峰值内存: {peak_memory - start_memory:.2f} MB")
        print(f"  处理区域数: {len(df) if not df.empty else 0}")
        
        # 清理内存
        del df, pcd, min_thickness, max_thickness, cmap
        gc.collect()
        
        return end_time - start_time, peak_memory - start_memory, len(df) if 'df' in locals() and not df.empty else 0
        
    except Exception as e:
        print(f"超高效版本测试失败: {e}")
        return None, None, 0

def test_optimized_version(file_path):
    """测试普通优化版本性能"""
    print("\n=== 测试普通优化版本 ===")
    start_time = time.time()
    start_memory = get_memory_usage()
    peak_memory = start_memory
    
    try:
        # 导入普通优化版本函数
        from main_optimized import measure_and_color_thickness_optimized
        
        # 监控内存使用
        def monitor_memory():
            nonlocal peak_memory
            while True:
                current_memory = get_memory_usage()
                if current_memory > peak_memory:
                    peak_memory = current_memory
                time.sleep(0.5)
        
        import threading
        monitor_thread = threading.Thread(target=monitor_memory, daemon=True)
        monitor_thread.start()
        
        # 运行普通优化版本
        df, pcd, min_thickness, max_thickness, cmap = measure_and_color_thickness_optimized(
            file_path, region_size=70.0, num_samples=8
        )
        
        end_time = time.time()
        end_memory = get_memory_usage()
        
        print(f"普通优化版本结果:")
        print(f"  处理时间: {end_time - start_time:.2f} 秒")
        print(f"  内存使用: {end_memory - start_memory:.2f} MB")
        print(f"  峰值内存: {peak_memory - start_memory:.2f} MB")
        print(f"  处理区域数: {len(df) if not df.empty else 0}")
        
        # 清理内存
        del df, pcd, min_thickness, max_thickness, cmap
        gc.collect()
        
        return end_time - start_time, peak_memory - start_memory, len(df) if 'df' in locals() and not df.empty else 0
        
    except Exception as e:
        print(f"普通优化版本测试失败: {e}")
        return None, None, 0

def select_file():
    """选择测试文件"""
    root = tk.Tk()
    root.withdraw()
    file_path = filedialog.askopenfilename(
        title="选择要测试的PLY文件",
        filetypes=[("Point Cloud Files", "*.ply *.pcd")]
    )
    root.destroy()
    return file_path

def main():
    """主测试函数"""
    print("=== 超高效版本性能对比测试 ===")
    print("此脚本将对比普通优化版本和超高效版本的性能")
    print("请选择要测试的PLY文件...")
    
    file_path = select_file()
    if not file_path:
        print("未选择文件，退出测试")
        return
    
    # 获取文件信息
    file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
    print(f"\n文件信息:")
    print(f"  文件路径: {file_path}")
    print(f"  文件大小: {file_size:.2f} MB")
    
    # 获取系统信息
    print(f"\n系统信息:")
    print(f"  CPU核心数: {psutil.cpu_count()}")
    print(f"  可用内存: {psutil.virtual_memory().available / (1024**3):.2f} GB")
    
    # 测试超高效版本
    ultra_time, ultra_memory, ultra_regions = test_ultra_optimized_version(file_path)
    
    # 等待一段时间让系统稳定
    time.sleep(3)
    
    # 测试普通优化版本
    optimized_time, optimized_memory, optimized_regions = test_optimized_version(file_path)
    
    # 性能对比
    print("\n" + "="*60)
    print("性能对比结果:")
    print("="*60)
    
    if ultra_time and optimized_time:
        speedup = optimized_time / ultra_time
        memory_ratio = ultra_memory / optimized_memory if optimized_memory > 0 else 1
        
        print(f"处理时间对比:")
        print(f"  普通优化版本: {optimized_time:.2f} 秒")
        print(f"  超高效版本:   {ultra_time:.2f} 秒")
        print(f"  加速比:       {speedup:.2f}x")
        
        print(f"\n内存使用对比:")
        print(f"  普通优化版本: {optimized_memory:.2f} MB")
        print(f"  超高效版本:   {ultra_memory:.2f} MB")
        print(f"  内存节省:     {(1-memory_ratio)*100:.1f}%")
        
        print(f"\n区域处理对比:")
        print(f"  普通优化版本: {optimized_regions} 个区域")
        print(f"  超高效版本:   {ultra_regions} 个区域")
        
        # 预估800MB文件的处理时间
        if file_size > 0:
            scale_factor = 800 / file_size
            estimated_optimized = optimized_time * scale_factor
            estimated_ultra = ultra_time * scale_factor
            
            print(f"\n800MB文件预估处理时间:")
            print(f"  普通优化版本预估: {estimated_optimized/60:.1f} 分钟")
            print(f"  超高效版本预估:   {estimated_ultra/60:.1f} 分钟")
            
            if estimated_ultra <= 300:  # 5分钟 = 300秒
                print(f"  ✓ 超高效版本预计可在5分钟内完成800MB文件处理")
            elif estimated_ultra <= 600:  # 10分钟 = 600秒
                print(f"  ✓ 超高效版本预计可在10分钟内完成800MB文件处理")
            else:
                print(f"  ⚠ 超高效版本预计需要 {estimated_ultra/60:.1f} 分钟处理800MB文件")
        
        print(f"\n性能总结:")
        print(f"  超高效版本相比普通优化版本:")
        print(f"  - 速度提升: {speedup:.1f}倍")
        print(f"  - 内存节省: {(1-memory_ratio)*100:.1f}%")
        print(f"  - 更适合处理大文件")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
