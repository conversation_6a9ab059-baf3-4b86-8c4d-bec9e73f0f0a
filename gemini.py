import open3d as o3d
import numpy as np
import pandas as pd
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import matplotlib
from matplotlib import cm
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import time

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# ========== 工具函数 (无改动) ==========
def load_point_cloud(file_path):
    """加载点云文件"""
    start_time = time.time()
    try:
        pcd = o3d.io.read_point_cloud(file_path)
        if not pcd.has_points():
            print("警告: 文件加载成功，但点云不包含任何点。")
            return None
        print(f"加载点云耗时 {time.time() - start_time:.2f} 秒")
        return pcd
    except Exception as e:
        print(f"加载点云文件失败: {e}")
        return None

def get_point_cloud_color_classes(pcd):
    """分离黄色和蓝色点云"""
    points = np.asarray(pcd.points)
    colors = np.asarray(pcd.colors)
    yellow_mask = np.all(np.abs(colors - [1, 1, 0]) < 0.2, axis=1)
    blue_mask = np.all(np.abs(colors - [0, 0, 1]) < 0.2, axis=1)
    return points[yellow_mask], points[blue_mask]

def compute_normals(pcd, knn=50):
    """计算点云法向量"""
    start_time = time.time()
    pcd.estimate_normals(search_param=o3d.geometry.KDTreeSearchParamKNN(knn=knn))
    normals = np.asarray(pcd.normals)
    print(f"计算法向量耗时 {time.time() - start_time:.2f} 秒")
    return normals

def compute_perpendicular_distance(point_a, point_b, normal):
    """计算点B到点A所在平面的垂直距离"""
    vector_ab = point_b - point_a
    return np.abs(np.dot(vector_ab, normal))

def create_continuous_colormap():
    """创建连续颜色映射"""
    return cm.get_cmap('viridis')

def get_color_for_thickness(thickness, cmap):
    """根据厚度值获取连续颜色，固定范围 [0, 1] mm"""
    fixed_min, fixed_max = 0.0, 1.0
    norm_thickness = (thickness - fixed_min) / (fixed_max - fixed_min) if fixed_max != fixed_min else 0.5
    norm_thickness = np.clip(norm_thickness, 0, 1)
    rgba = cmap(norm_thickness)
    return rgba[:3]

# ========== 优化后的核心厚度测量和着色函数 (修改: 返回每点厚度) ==========
def measure_and_color_thickness_accelerated(file_path, voxel_size=1.0):
    total_start_time = time.time()
    print("="*30 + "\n开始执行加速版厚度测量流程\n" + "="*30)

    pcd_original = load_point_cloud(file_path)
    if pcd_original is None:
        messagebox.showerror("错误", "加载点云失败或点云为空。")
        return pd.DataFrame(), None, 0, 1, None, None, None

    print(f"原始点云数量: {len(pcd_original.points)}")
    if not pcd_original.has_colors():
        messagebox.showerror("错误", "点云文件不包含颜色信息。")
        return pd.DataFrame(), pcd_original, 0, 1, None, None, None

    print(f"步骤 1: 体素降采样 (voxel_size = {voxel_size} mm)...")
    pcd_downsampled = pcd_original.voxel_down_sample(voxel_size=voxel_size)
    print(f"降采样后点云数量: {len(pcd_downsampled.points)}")
    if len(pcd_downsampled.points) == 0:
        messagebox.showerror("错误", f"降采样后点云为空，请尝试更小的 voxel_size。")
        return pd.DataFrame(), pcd_original, 0, 1, None, None, None

    print("    正在为降采样点恢复颜色...")
    original_kdtree = o3d.geometry.KDTreeFlann(pcd_original)
    downsampled_points = np.asarray(pcd_downsampled.points)
    new_colors = np.array([np.asarray(pcd_original.colors)[original_kdtree.search_knn_vector_3d(p, 1)[1][0]] for p in downsampled_points])
    pcd_downsampled.colors = o3d.utility.Vector3dVector(new_colors)

    print("步骤 2: 在降采样点云上进行厚度计算...")
    a_points, b_points = get_point_cloud_color_classes(pcd_downsampled)
    if len(a_points) == 0 or len(b_points) == 0:
        messagebox.showwarning("警告", "降采样后未能找到有效的A/B类点。请尝试减小 voxel_size。")
        return pd.DataFrame(), pcd_original, 0, 1, None, None, None

    print(f"    降采样后 A类点数: {len(a_points)}, B类点数: {len(b_points)}")
    
    # 策略：遍历点少的一方，向点多的一方查询，并将厚度存在点多的一方
    if len(a_points) > len(b_points):
        major_points, minor_points, major_name = a_points, b_points, "A"
    else:
        major_points, minor_points, major_name = b_points, a_points, "B"
        
    major_pcd = o3d.geometry.PointCloud(points=o3d.utility.Vector3dVector(major_points))
    print(f"    计算{major_name}类点云法向量...")
    major_normals = compute_normals(major_pcd, knn=20)
    major_kdtree = o3d.geometry.KDTreeFlann(major_pcd)

    print(f"    计算厚度...")
    thickness_map = np.full(len(major_points), np.nan)
    for minor_point in minor_points:
        _, idx, _ = major_kdtree.search_knn_vector_3d(minor_point, 1)
        major_idx = idx[0]
        thickness_map[major_idx] = compute_perpendicular_distance(major_points[major_idx], minor_point, major_normals[major_idx])

    valid_mask = ~np.isnan(thickness_map)
    points_with_thickness = major_points[valid_mask]
    final_thicknesses = thickness_map[valid_mask]

    if len(final_thicknesses) == 0:
        messagebox.showwarning("警告", "未能计算出任何有效的厚度值。")
        return pd.DataFrame(), pcd_original, 0, 1, None, None, None

    print("步骤 3: 将厚度结果映射回原始点云...")
    pcd_ref = o3d.geometry.PointCloud(points=o3d.utility.Vector3dVector(points_with_thickness))
    ref_kdtree = o3d.geometry.KDTreeFlann(pcd_ref)
    
    original_points = np.asarray(pcd_original.points)
    # 找到原始点云中每个点，在参考点云中最近邻的索引
    indices = np.array([ref_kdtree.search_knn_vector_3d(p, 1)[1][0] for p in original_points])
    
    # 根据索引，为原始点云的每个点分配厚度值和颜色
    per_point_thickness = final_thicknesses[indices]
    new_original_colors = np.array([get_color_for_thickness(t, create_continuous_colormap()) for t in per_point_thickness])
    
    pcd_original.colors = o3d.utility.Vector3dVector(new_original_colors)
    
    avg_thickness = np.mean(final_thicknesses)
    stats_df = pd.DataFrame([
        {"Region": "平均厚度", "Value": f"{avg_thickness:.4f} mm"},
        {"Region": "最小厚度", "Value": f"{np.min(final_thicknesses):.4f} mm"},
        {"Region": "最大厚度", "Value": f"{np.max(final_thicknesses):.4f} mm"},
        {"Region": "厚度标准差", "Value": f"{np.std(final_thicknesses):.4f} mm"},
    ])
    plot_df = pd.DataFrame([{"Region": "Overall", "Thickness": avg_thickness}])

    print("="*30 + f"\n处理完成！总耗时: {time.time() - total_start_time:.2f} 秒\n" + "="*30)
    
    return plot_df, pcd_original, 0.0, 1.0, create_continuous_colormap(), stats_df, per_point_thickness

# ========== 交互界面 (修改: 加入交互式点拾取) ==========
def display_table(plot_df, pcd, min_thickness, max_thickness, cmap, stats_df, per_point_thickness):
    root = tk.Tk()
    root.title("厚度测量结果")
    root.geometry("1000x800")
    try:
        root.option_add("*Font", "SimHei 12")
    except tk.TclError:
        root.option_add("*Font", "Arial 12")

    root.protocol("WM_DELETE_WINDOW", root.destroy)

    if stats_df.empty:
        messagebox.showwarning("警告", "无有效厚度数据")
        root.destroy()
        return

    main_frame = ttk.Frame(root)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # ... (左侧表格和右侧图表的代码不变)
    left_frame = ttk.Frame(main_frame)
    left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
    title_label = ttk.Label(left_frame, text="厚度测量统计结果", font=("SimHei", 14, "bold"))
    title_label.pack(side=tk.TOP, pady=5)
    tree = ttk.Treeview(left_frame, columns=("Stat", "Value"), show="headings", height=10)
    tree.heading("Stat", text="统计项"); tree.heading("Value", text="值")
    tree.column("Stat", width=150, anchor="center"); tree.column("Value", width=150, anchor="center")
    for _, row in stats_df.iterrows():
        tree.insert("", "end", values=(row['Region'], row['Value']))
    tree.pack(fill=tk.BOTH, expand=True)

    right_frame = ttk.Frame(main_frame)
    right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
    fig = Figure(figsize=(6, 8), dpi=100); fig.subplots_adjust(hspace=0.5)
    ax1 = fig.add_subplot(211); ax2 = fig.add_subplot(212)
    ax1.bar(plot_df["Region"], plot_df["Thickness"], color='darkblue', width=0.4)
    ax1.set_title("总体平均厚度", fontsize=14); ax1.set_ylabel("平均厚度 (mm)", fontsize=12)
    ax1.grid(True, linestyle='--', alpha=0.7, axis='y')
    for index, value in enumerate(plot_df["Thickness"]):
        ax1.text(index, value, f'{value:.4f}', ha='center', va='bottom')
    ax2.set_title('厚度-颜色映射关系 (固定范围 0-1 mm)', fontsize=14); ax2.axis('off')
    cbar = fig.colorbar(plt.cm.ScalarMappable(cmap=cmap, norm=plt.Normalize(0.0, 1.0)), 
                        ax=ax2, orientation='horizontal', pad=0.2, aspect=40)
    cbar.set_label('厚度 (mm)', fontsize=12); cbar.ax.tick_params(labelsize=10)
    canvas = FigureCanvasTkAgg(fig, master=right_frame)
    canvas.draw(); canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    # ... (以上部分未变)
    
    button_frame = ttk.Frame(root)
    button_frame.pack(fill=tk.X, padx=10, pady=10)

    # --- 新增的交互式查看功能 ---
    def show_point_cloud_interactive():
        print("\n--- 启动交互式3D点云查看器 ---")
        print("使用方法: 按住 [Ctrl] + 鼠标左键点击点云中的点，可在控制台查看其厚度。")
        
        # 这个内部函数是回调，当拾取点时由Open3D调用
        def pick_points(vis):
            print("\n检测到点拾取事件...")
            picked = vis.get_picked_points()
            if len(picked) > 0:
                # 获取被拾取点的索引
                idx = picked[0].index
                point_coord = np.asarray(pcd.points)[idx]
                # 使用索引从我们保存的厚度数组中获取厚度值
                point_thickness = per_point_thickness[idx]
                
                print(f"  > 选中点索引: {idx}")
                print(f"  > 坐标 (X,Y,Z): ({point_coord[0]:.2f}, {point_coord[1]:.2f}, {point_coord[2]:.2f})")
                print(f"  > 此点厚度: {point_thickness:.4f} mm")
            return False

        try:
            vis = o3d.visualization.VisualizerWithEditing()
            vis.create_window(window_name="交互式厚度查看 (Ctrl+Click拾取点)", width=1280, height=720)
            vis.add_geometry(pcd)
            # 注册拾取点的回调函数
            vis.register_selection_changed_callback(pick_points)
            render_option = vis.get_render_option()
            render_option.point_size = 2.0
            vis.add_geometry(o3d.geometry.TriangleMesh.create_coordinate_frame(size=50.0))
            vis.run()
            vis.destroy_window()
            print("--- 可视化窗口已关闭 ---")
        except Exception as e:
            messagebox.showerror("错误", f"无法显示点云: {e}")

    # 将按钮的命令指向新的交互式函数
    view_button = ttk.Button(button_frame, text="查看3D点云", command=show_point_cloud_interactive)
    view_button.pack(side=tk.RIGHT, padx=10, pady=5, ipadx=10, ipady=5)

    def save_point_cloud():
        file_path = filedialog.asksaveasfilename(defaultextension=".ply", filetypes=[("PLY files", "*.ply"), ("PCD files", "*.pcd")])
        if file_path:
            try:
                o3d.io.write_point_cloud(file_path, pcd, write_ascii=True)
                messagebox.showinfo("保存成功", f"点云已保存至: {file_path}")
            except Exception as e:
                messagebox.showerror("保存失败", f"保存点云时发生错误: {e}")

    save_button = ttk.Button(button_frame, text="保存点云", command=save_point_cloud)
    save_button.pack(side=tk.RIGHT, padx=10, pady=5, ipadx=10, ipady=5)

    root.mainloop()

def select_file():
    root = tk.Tk()
    root.withdraw()
    return filedialog.askopenfilename(title="请选择点云文件", filetypes=[("Point Cloud Files", "*.ply *.pcd")])

# ========== 主函数 (修改以处理新的返回值) ==========
def main():
    file_path = select_file()
    if not file_path:
        print("未选择文件，程序退出。")
        return

    plot_df, pcd, min_t, max_t, cmap, stats_df, per_point_thickness = measure_and_color_thickness_accelerated(file_path, voxel_size=1.0)

    if plot_df.empty or pcd is None:
        print("因处理过程中出错，无法显示结果。")
        return

    display_table(plot_df, pcd, min_t, max_t, cmap, stats_df, per_point_thickness)

if __name__ == "__main__":
    main()