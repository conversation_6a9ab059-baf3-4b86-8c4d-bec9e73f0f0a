import open3d as o3d
import numpy as np
import pandas as pd
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import matplotlib
from matplotlib import cm
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import time
from multiprocessing import Pool, cpu_count, shared_memory
from functools import partial
import gc
from scipy.spatial import cKDTree
from numba import jit, prange
import warnings
import threading
from concurrent.futures import ThreadPoolExecutor
warnings.filterwarnings('ignore')

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# ========== 超高效工具函数 ==========
def load_point_cloud_streaming(file_path, max_points=None):
    """流式加载点云，支持大文件分块处理"""
    start_time = time.time()
    print(f"开始流式加载点云文件: {file_path}")
    
    pcd = o3d.io.read_point_cloud(file_path)
    points = np.asarray(pcd.points, dtype=np.float32)
    colors = np.asarray(pcd.colors, dtype=np.float32) if pcd.has_colors() else np.ones((len(points), 3), dtype=np.float32) * 0.5
    
    # 如果点数过多，进行智能采样
    if max_points and len(points) > max_points:
        print(f"点数过多({len(points)})，进行智能采样到{max_points}个点")
        indices = np.random.choice(len(points), max_points, replace=False)
        points = points[indices]
        colors = colors[indices]
        
        # 更新点云对象
        pcd.points = o3d.utility.Vector3dVector(points)
        pcd.colors = o3d.utility.Vector3dVector(colors)
    
    print(f"加载点云耗时 {time.time() - start_time:.2f} 秒, 点数: {len(points)}")
    return pcd, points, colors

@jit(nopython=True, cache=True)
def get_color_masks_ultra_fast(colors):
    """超快速颜色分类，使用缓存JIT"""
    n = len(colors)
    yellow_indices = np.zeros(n, dtype=np.int32)
    blue_indices = np.zeros(n, dtype=np.int32)
    yellow_count = 0
    blue_count = 0

    for i in range(n):
        # 黄色检测 (更宽松的阈值以减少计算)
        if (colors[i, 0] > 0.9 and colors[i, 1] > 0.9 and colors[i, 2] < 0.1):
            yellow_indices[yellow_count] = i
            yellow_count += 1
        # 蓝色检测
        elif (colors[i, 0] < 0.1 and colors[i, 1] < 0.1 and colors[i, 2] > 0.9):
            blue_indices[blue_count] = i
            blue_count += 1

    return yellow_indices[:yellow_count], blue_indices[:blue_count]

def compute_normals_fast(points, knn=20):
    """快速法向量计算，减少KNN"""
    start_time = time.time()
    
    # 对于大点云，使用更少的邻居点
    if len(points) > 500000:
        knn = 15
    elif len(points) > 200000:
        knn = 20
    
    temp_pcd = o3d.geometry.PointCloud()
    temp_pcd.points = o3d.utility.Vector3dVector(points)
    temp_pcd.estimate_normals(
        search_param=o3d.geometry.KDTreeSearchParamKNN(knn=knn),
        fast_normal_computation=True
    )
    
    normals = np.asarray(temp_pcd.normals, dtype=np.float32)
    print(f"快速法向量计算耗时 {time.time() - start_time:.2f} 秒")
    return normals

@jit(nopython=True, cache=True)
def compute_distance_vectorized(points_a, points_b, normals):
    """向量化距离计算"""
    n = len(points_a)
    distances = np.zeros(n, dtype=np.float32)
    
    for i in range(n):
        diff = points_a[i] - points_b[i]
        distances[i] = abs(diff[0] * normals[i, 0] + 
                          diff[1] * normals[i, 1] + 
                          diff[2] * normals[i, 2])
    return distances

def process_region_memory_efficient(args):
    """内存高效的区域处理"""
    (region_idx, x_start, x_end, y_start, y_end, 
     a_points, a_normals, b_points, b_normals, num_samples) = args
    
    try:
        # 使用内存映射的KDTree
        a_region_mask = ((a_points[:, 0] >= x_start) & (a_points[:, 0] <= x_end) & 
                        (a_points[:, 1] >= y_start) & (a_points[:, 1] <= y_end))
        b_region_mask = ((b_points[:, 0] >= x_start) & (b_points[:, 0] <= x_end) & 
                        (b_points[:, 1] >= y_start) & (b_points[:, 1] <= y_end))
        
        a_region_points = a_points[a_region_mask]
        b_region_points = b_points[b_region_mask]
        
        if len(a_region_points) < num_samples or len(b_region_points) < num_samples:
            return None
        
        # 减少采样数量以节省内存
        actual_samples = min(num_samples, len(a_region_points)//10, len(b_region_points)//10)
        if actual_samples < 3:
            actual_samples = min(num_samples, len(a_region_points), len(b_region_points))
        
        # 随机采样而不是KDTree搜索（更快，更省内存）
        a_sample_indices = np.random.choice(len(a_region_points), actual_samples, replace=False)
        b_sample_indices = np.random.choice(len(b_region_points), actual_samples, replace=False)
        
        a_samples = a_region_points[a_sample_indices]
        b_samples = b_region_points[b_sample_indices]
        a_sample_normals = a_normals[np.where(a_region_mask)[0][a_sample_indices]]
        b_sample_normals = b_normals[np.where(b_region_mask)[0][b_sample_indices]]
        
        # 简化的距离计算
        thicknesses = []
        
        # 使用最近邻近似（避免构建完整KDTree）
        for i in range(len(a_samples)):
            # 找到最近的B点（简化搜索）
            distances_to_b = np.sum((b_region_points - a_samples[i])**2, axis=1)
            closest_b_idx = np.argmin(distances_to_b)
            closest_b = b_region_points[closest_b_idx]
            
            # 计算垂直距离
            diff = a_samples[i] - closest_b
            thickness = abs(np.dot(diff, a_sample_normals[i]))
            thicknesses.append(thickness)
        
        # 对B点做同样处理
        for i in range(len(b_samples)):
            distances_to_a = np.sum((a_region_points - b_samples[i])**2, axis=1)
            closest_a_idx = np.argmin(distances_to_a)
            closest_a = a_region_points[closest_a_idx]
            
            diff = b_samples[i] - closest_a
            thickness = abs(np.dot(diff, b_sample_normals[i]))
            thicknesses.append(thickness)
        
        if len(thicknesses) >= 4:  # 降低最小要求
            thicknesses = np.array(thicknesses)
            # 去除异常值
            q25, q75 = np.percentile(thicknesses, [25, 75])
            iqr = q75 - q25
            lower_bound = q25 - 1.5 * iqr
            upper_bound = q75 + 1.5 * iqr
            filtered_thickness = thicknesses[(thicknesses >= lower_bound) & (thicknesses <= upper_bound)]
            
            if len(filtered_thickness) > 0:
                region_thickness = np.mean(filtered_thickness)
                return {
                    "Region": region_idx,
                    "Thickness": region_thickness,
                    "x_start": x_start,
                    "x_end": x_end,
                    "y_start": y_start,
                    "y_end": y_end
                }
    except Exception as e:
        print(f"区域 {region_idx} 处理出错: {e}")
        return None

def create_continuous_colormap():
    """创建连续颜色映射"""
    return cm.get_cmap('viridis')

def get_color_for_thickness(thickness, cmap):
    """根据厚度值获取连续颜色"""
    fixed_min, fixed_max = 0.0, 1.0
    norm_thickness = (thickness - fixed_min) / (fixed_max - fixed_min) if fixed_max != fixed_min else 0.5
    norm_thickness = np.clip(norm_thickness, 0, 1)
    rgba = cmap(norm_thickness)
    return rgba[:3]

# ========== 超高效主处理函数 ==========
def measure_and_color_thickness_ultra(file_path, region_size=100.0, num_samples=6, max_points=1000000):
    """超高效版本的厚度测量和着色函数"""
    start_time = time.time()

    # 1. 流式加载，支持大文件采样
    pcd, points, colors = load_point_cloud_streaming(file_path, max_points)

    # 2. 超快速颜色分类
    yellow_indices, blue_indices = get_color_masks_ultra_fast(colors)

    if len(yellow_indices) == 0 or len(blue_indices) == 0:
        print("未找到有效的A类或B类点")
        return pd.DataFrame(), pcd, 0.0, 1.0, None

    # yellow_indices和blue_indices已经是numpy数组

    a_points = points[yellow_indices].copy()
    b_points = points[blue_indices].copy()

    print(f"A类点数: {len(a_points)}, B类点数: {len(b_points)}")

    # 3. 快速法向量计算
    a_normals = compute_normals_fast(a_points)
    b_normals = compute_normals_fast(b_points)

    # 4. 智能区域划分（更大的区域以减少计算量）
    x_min, x_max = points[:, 0].min(), points[:, 0].max()
    y_min, y_max = points[:, 1].min(), points[:, 1].max()
    x_range = x_max - x_min
    y_range = y_max - y_min

    # 大幅减少区域数量以提高速度
    total_area = x_range * y_range
    ideal_area_per_region = 150000  # 增大区域面积
    target_regions = max(4, min(50, round(total_area / ideal_area_per_region)))  # 限制最大区域数

    num_x = max(2, min(8, round(np.sqrt(target_regions * x_range / y_range))))
    num_y = max(2, min(8, round(target_regions / num_x)))

    x_step = x_range / num_x
    y_step = y_range / num_y

    print(f"超高效区域划分: {num_x}x{num_y} = {num_x * num_y} 个区域")
    print(f"每个区域大小: {x_step:.2f} x {y_step:.2f} mm")

    # 5. 准备轻量级多进程参数
    region_args = []
    region_idx = 0
    for i in range(num_x):
        for j in range(num_y):
            region_idx += 1
            x_start = x_min + i * x_step
            x_end = x_start + x_step
            y_start = y_min + j * y_step
            y_end = y_start + y_step

            region_args.append((
                region_idx, x_start, x_end, y_start, y_end,
                a_points, a_normals, b_points, b_normals, num_samples
            ))

    # 6. 多线程处理（比多进程更省内存）
    print(f"开始多线程处理 {len(region_args)} 个区域...")
    process_start = time.time()

    # 使用线程池而不是进程池（更省内存）
    max_workers = min(8, cpu_count())  # 限制线程数
    print(f"使用 {max_workers} 个线程")

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        results = list(executor.map(process_region_memory_efficient, region_args))

    # 过滤有效结果
    valid_results = [r for r in results if r is not None]
    print(f"多线程处理耗时: {time.time() - process_start:.2f} 秒")
    print(f"成功处理 {len(valid_results)} 个区域")

    if not valid_results:
        print("没有有效的区域结果")
        return pd.DataFrame(), pcd, 0.0, 1.0, None

    # 7. 高效点云着色（批量处理）
    print("开始高效点云着色...")
    color_start = time.time()

    df = pd.DataFrame(valid_results)
    cmap = create_continuous_colormap()
    new_colors = colors.copy()

    # 批量处理颜色更新
    for result in valid_results:
        x_start, x_end = result['x_start'], result['x_end']
        y_start, y_end = result['y_start'], result['y_end']
        thickness = result['Thickness']

        # 区域掩码
        region_mask = ((points[:, 0] >= x_start) & (points[:, 0] <= x_end) &
                      (points[:, 1] >= y_start) & (points[:, 1] <= y_end))

        # 只对黄色和蓝色点着色
        yellow_mask = np.isin(np.arange(len(points)), yellow_indices)
        blue_mask = np.isin(np.arange(len(points)), blue_indices)
        color_mask = region_mask & (yellow_mask | blue_mask)

        if np.any(color_mask):
            new_colors[color_mask] = get_color_for_thickness(thickness, cmap)

    # 应用新颜色
    pcd.colors = o3d.utility.Vector3dVector(new_colors)

    print(f"点云着色耗时: {time.time() - color_start:.2f} 秒")
    print(f"总处理耗时: {time.time() - start_time:.2f} 秒")

    # 强制垃圾回收
    del a_points, b_points, a_normals, b_normals, yellow_indices, blue_indices
    gc.collect()

    return df, pcd, 0.0, 1.0, cmap

# ========== 保持原有显示界面（简化版本） ==========
def display_table_simple(df, pcd, min_thickness, max_thickness, cmap):
    """简化的显示界面，减少内存占用"""
    start_time = time.time()
    root = tk.Tk()
    root.title("厚度测量结果 - 超高效版本")
    root.geometry("800x600")  # 减小窗口大小

    try:
        root.option_add("*Font", "SimHei 10")  # 减小字体
    except:
        root.option_add("*Font", "Arial 10")

    def on_close():
        root.quit()
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_close)

    if df.empty:
        messagebox.showwarning("警告", "无有效厚度数据")
        root.destroy()
        return

    main_frame = ttk.Frame(root)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    # 简化的表格显示
    title_label = ttk.Label(main_frame, text="区域厚度测量结果 (超高效版本)", font=("SimHei", 12, "bold"))
    title_label.pack(side=tk.TOP, pady=5)

    # 创建简化的表格
    tree_frame = ttk.Frame(main_frame)
    tree_frame.pack(fill=tk.BOTH, expand=True, pady=5)

    tree = ttk.Treeview(tree_frame, columns=("Region", "Thickness"), show="headings", height=15)
    tree.heading("Region", text="区域")
    tree.heading("Thickness", text="厚度(mm)")
    tree.column("Region", width=100, anchor="center")
    tree.column("Thickness", width=120, anchor="center")

    # 添加滚动条
    scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=tree.yview)
    tree.configure(yscrollcommand=scrollbar.set)

    # 填充数据
    for _, row in df.iterrows():
        values = [f"区域{int(row['Region'])}", f"{row['Thickness']:.4f}"]
        tree.insert("", "end", values=values)

    tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    # 简化的按钮区域
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=5)

    def show_point_cloud():
        try:
            vis = o3d.visualization.Visualizer()
            vis.create_window(window_name="厚度着色点云 (超高效版本)", width=800, height=600)
            vis.add_geometry(pcd)
            render_option = vis.get_render_option()
            render_option.point_size = 1.5  # 减小点大小以提高渲染速度
            vis.run()
            vis.destroy_window()
        except Exception as e:
            print(f"显示点云出错: {e}")
            messagebox.showerror("错误", f"无法显示点云: {e}")

    def save_point_cloud():
        file_path = filedialog.asksaveasfilename(
            defaultextension=".ply",
            filetypes=[("PLY files", "*.ply"), ("PCD files", "*.pcd")]
        )
        if file_path:
            o3d.io.write_point_cloud(file_path, pcd)
            messagebox.showinfo("保存成功", f"点云已保存至: {file_path}")

    # 统计信息
    stats_label = ttk.Label(button_frame,
                           text=f"处理区域: {len(df)} | 平均厚度: {df['Thickness'].mean():.4f}mm | 标准差: {df['Thickness'].std():.4f}mm")
    stats_label.pack(side=tk.LEFT, padx=5)

    view_button = ttk.Button(button_frame, text="查看3D点云", command=show_point_cloud)
    view_button.pack(side=tk.RIGHT, padx=5)

    save_button = ttk.Button(button_frame, text="保存点云", command=save_point_cloud)
    save_button.pack(side=tk.RIGHT, padx=5)

    print(f"显示界面设置耗时 {time.time() - start_time:.2f} 秒")
    root.mainloop()

def select_file():
    """选择点云文件"""
    root = tk.Tk()
    root.withdraw()
    return filedialog.askopenfilename(filetypes=[("Point Cloud Files", "*.ply *.pcd")])

# ========== 超高效版本主函数 ==========
def main():
    """超高效版本主函数"""
    print("=== 点云厚度测量程序 - 超高效版本 ===")
    print("主要优化:")
    print("1. 智能采样减少内存占用")
    print("2. 多线程替代多进程节省内存")
    print("3. 大区域划分减少计算量")
    print("4. 简化算法提高速度")
    print("5. 流式处理支持超大文件")
    print("6. 缓存JIT编译加速")
    print("=====================================")

    file_path = select_file()
    if not file_path:
        print("未选择文件")
        return

    # 根据文件大小自动调整参数
    import os
    file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
    print(f"文件大小: {file_size_mb:.1f} MB")

    # 动态调整参数
    if file_size_mb > 500:  # 大于500MB
        max_points = 800000
        region_size = 120.0
        num_samples = 5
        print("检测到大文件，使用超高效模式")
    elif file_size_mb > 200:  # 大于200MB
        max_points = 1200000
        region_size = 100.0
        num_samples = 6
        print("检测到中等文件，使用高效模式")
    else:  # 小文件
        max_points = None
        region_size = 80.0
        num_samples = 8
        print("检测到小文件，使用标准模式")

    df, pcd, min_thickness, max_thickness, cmap = measure_and_color_thickness_ultra(
        file_path, region_size=region_size, num_samples=num_samples, max_points=max_points
    )

    if df.empty:
        messagebox.showwarning("警告", "无法计算出有效厚度数据")
        return

    display_table_simple(df, pcd, min_thickness, max_thickness, cmap)

if __name__ == "__main__":
    main()
