"""
超高效版本快速功能测试
"""
import sys
import time
import numpy as np

def test_ultra_imports():
    """测试超高效版本的导入"""
    print("测试超高效版本导入...")
    try:
        from main_ultra_optimized import (
            get_color_masks_ultra_fast, 
            compute_distance_vectorized,
            load_point_cloud_streaming
        )
        print("✓ 超高效版本函数导入成功")
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_ultra_functions():
    """测试超高效版本的关键函数"""
    print("\n测试超高效版本函数...")
    try:
        from main_ultra_optimized import get_color_masks_ultra_fast, compute_distance_vectorized
        
        # 测试颜色分类函数
        test_colors = np.array([
            [1.0, 1.0, 0.0],  # 黄色
            [0.0, 0.0, 1.0],  # 蓝色
            [0.5, 0.5, 0.5],  # 灰色
            [0.95, 0.95, 0.05],  # 接近黄色
        ], dtype=np.float32)
        
        yellow_indices, blue_indices = get_color_masks_ultra_fast(test_colors)
        print(f"✓ 超快速颜色分类测试成功，黄色点: {len(yellow_indices)}, 蓝色点: {len(blue_indices)}")
        
        # 测试向量化距离计算
        points_a = np.array([[0, 0, 0], [1, 1, 1]], dtype=np.float32)
        points_b = np.array([[1, 0, 0], [2, 1, 1]], dtype=np.float32)
        normals = np.array([[1, 0, 0], [0, 1, 0]], dtype=np.float32)
        
        distances = compute_distance_vectorized(points_a, points_b, normals)
        print(f"✓ 向量化距离计算测试成功，距离: {distances}")
        
        return True
    except Exception as e:
        print(f"✗ 超高效函数测试失败: {e}")
        return False

def test_performance_comparison():
    """测试性能对比"""
    print("\n测试性能对比...")
    try:
        from main_ultra_optimized import get_color_masks_ultra_fast
        from numba import jit
        import numpy as np
        
        # 创建大量测试数据
        n_points = 100000
        test_colors = np.random.rand(n_points, 3).astype(np.float32)
        # 添加一些黄色和蓝色点
        test_colors[::100] = [1.0, 1.0, 0.0]  # 黄色
        test_colors[1::100] = [0.0, 0.0, 1.0]  # 蓝色
        
        # 测试超高效版本
        start_time = time.time()
        yellow_indices, blue_indices = get_color_masks_ultra_fast(test_colors)
        ultra_time = time.time() - start_time
        
        print(f"✓ 超高效版本处理{n_points}个点耗时: {ultra_time:.4f} 秒")
        print(f"  找到黄色点: {len(yellow_indices)}, 蓝色点: {len(blue_indices)}")
        
        # 测试普通版本（如果存在）
        try:
            @jit(nopython=True)
            def normal_color_classification(colors):
                yellow_mask = np.zeros(len(colors), dtype=np.bool_)
                blue_mask = np.zeros(len(colors), dtype=np.bool_)
                for i in range(len(colors)):
                    if (abs(colors[i, 0] - 1.0) < 0.1 and 
                        abs(colors[i, 1] - 1.0) < 0.1 and 
                        abs(colors[i, 2] - 0.0) < 0.1):
                        yellow_mask[i] = True
                    elif (abs(colors[i, 0] - 0.0) < 0.1 and 
                          abs(colors[i, 1] - 0.0) < 0.1 and 
                          abs(colors[i, 2] - 1.0) < 0.1):
                        blue_mask[i] = True
                return yellow_mask, blue_mask
            
            start_time = time.time()
            yellow_mask, blue_mask = normal_color_classification(test_colors)
            normal_time = time.time() - start_time
            
            speedup = normal_time / ultra_time if ultra_time > 0 else 1
            print(f"  普通版本耗时: {normal_time:.4f} 秒")
            print(f"  加速比: {speedup:.2f}x")
            
        except:
            print("  无法测试普通版本对比")
        
        return True
    except Exception as e:
        print(f"✗ 性能对比测试失败: {e}")
        return False

def test_memory_efficiency():
    """测试内存效率"""
    print("\n测试内存效率...")
    try:
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        start_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建大量数据测试内存使用
        n_points = 500000
        test_data = np.random.rand(n_points, 3).astype(np.float32)
        
        from main_ultra_optimized import get_color_masks_ultra_fast
        yellow_indices, blue_indices = get_color_masks_ultra_fast(test_data)
        
        end_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_used = end_memory - start_memory
        
        print(f"✓ 处理{n_points}个点的内存使用: {memory_used:.2f} MB")
        print(f"  平均每个点内存使用: {memory_used/n_points*1024:.2f} KB")
        
        # 清理内存
        del test_data, yellow_indices, blue_indices
        import gc
        gc.collect()
        
        return True
    except Exception as e:
        print(f"✗ 内存效率测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 超高效版本快速测试 ===")
    
    all_tests_passed = True
    
    # 测试导入
    if not test_ultra_imports():
        all_tests_passed = False
    
    # 测试函数
    if not test_ultra_functions():
        all_tests_passed = False
    
    # 测试性能
    if not test_performance_comparison():
        all_tests_passed = False
    
    # 测试内存效率
    if not test_memory_efficiency():
        all_tests_passed = False
    
    print("\n" + "="*50)
    if all_tests_passed:
        print("✓ 所有测试通过！超高效版本可以正常使用。")
        print("\n主要优势:")
        print("- 更快的处理速度")
        print("- 更低的内存占用")
        print("- 智能采样支持超大文件")
        print("- 多线程替代多进程节省内存")
        print("\n现在可以运行 main_ultra_optimized.py 处理您的PLY文件了。")
    else:
        print("✗ 部分测试失败，请检查代码。")
    
    print("="*50)

if __name__ == "__main__":
    main()
