import open3d as o3d
import numpy as np
import pandas as pd
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import matplotlib
from matplotlib import cm
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import time
from concurrent.futures import ThreadPoolExecutor
import gc
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# ========== 超快速工具函数 ==========
def load_point_cloud_smart(file_path):
    """智能加载点云，根据文件大小自动采样"""
    start_time = time.time()
    print(f"开始智能加载点云文件: {file_path}")
    
    # 获取文件大小
    file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
    print(f"文件大小: {file_size_mb:.1f} MB")
    
    pcd = o3d.io.read_point_cloud(file_path)
    points = np.asarray(pcd.points, dtype=np.float32)
    colors = np.asarray(pcd.colors, dtype=np.float32) if pcd.has_colors() else np.ones((len(points), 3), dtype=np.float32) * 0.5
    
    # 根据文件大小智能采样
    original_count = len(points)
    if file_size_mb > 800:  # 超大文件
        target_points = 600000
    elif file_size_mb > 400:  # 大文件
        target_points = 800000
    elif file_size_mb > 200:  # 中等文件
        target_points = 1000000
    else:  # 小文件
        target_points = original_count
    
    if original_count > target_points:
        print(f"点数过多({original_count})，智能采样到{target_points}个点")
        # 使用均匀采样保持点云分布
        step = original_count // target_points
        indices = np.arange(0, original_count, step)[:target_points]
        points = points[indices]
        colors = colors[indices]
        
        # 更新点云对象
        pcd.points = o3d.utility.Vector3dVector(points)
        pcd.colors = o3d.utility.Vector3dVector(colors)
    
    print(f"加载点云耗时 {time.time() - start_time:.2f} 秒, 最终点数: {len(points)}")
    return pcd, points, colors

def get_color_masks_fast(colors):
    """快速颜色分类，使用向量化操作"""
    # 黄色检测：RGB接近(1,1,0)
    yellow_mask = (colors[:, 0] > 0.9) & (colors[:, 1] > 0.9) & (colors[:, 2] < 0.1)
    # 蓝色检测：RGB接近(0,0,1)
    blue_mask = (colors[:, 0] < 0.1) & (colors[:, 1] < 0.1) & (colors[:, 2] > 0.9)
    
    return np.where(yellow_mask)[0], np.where(blue_mask)[0]

def compute_normals_efficient(points, knn=15):
    """高效法向量计算"""
    start_time = time.time()
    
    # 根据点数动态调整KNN
    if len(points) > 500000:
        knn = 10
    elif len(points) > 200000:
        knn = 15
    else:
        knn = 20
    
    temp_pcd = o3d.geometry.PointCloud()
    temp_pcd.points = o3d.utility.Vector3dVector(points)
    temp_pcd.estimate_normals(
        search_param=o3d.geometry.KDTreeSearchParamKNN(knn=knn),
        fast_normal_computation=True
    )
    
    normals = np.asarray(temp_pcd.normals, dtype=np.float32)
    print(f"高效法向量计算耗时 {time.time() - start_time:.2f} 秒")
    return normals

def process_region_super_fast(args):
    """超快速区域处理"""
    (region_idx, x_start, x_end, y_start, y_end, 
     a_points, a_normals, b_points, b_normals, num_samples) = args
    
    try:
        # 区域内点的掩码
        a_region_mask = ((a_points[:, 0] >= x_start) & (a_points[:, 0] <= x_end) & 
                        (a_points[:, 1] >= y_start) & (a_points[:, 1] <= y_end))
        b_region_mask = ((b_points[:, 0] >= x_start) & (b_points[:, 0] <= x_end) & 
                        (b_points[:, 1] >= y_start) & (b_points[:, 1] <= y_end))
        
        a_region_points = a_points[a_region_mask]
        b_region_points = b_points[b_region_mask]
        
        if len(a_region_points) < num_samples or len(b_region_points) < num_samples:
            return None
        
        # 简化采样：随机选择而不是KDTree搜索
        actual_samples = min(num_samples, len(a_region_points)//5, len(b_region_points)//5)
        if actual_samples < 3:
            actual_samples = min(num_samples, len(a_region_points), len(b_region_points))
        
        # 随机采样
        a_sample_indices = np.random.choice(len(a_region_points), actual_samples, replace=False)
        b_sample_indices = np.random.choice(len(b_region_points), actual_samples, replace=False)
        
        a_samples = a_region_points[a_sample_indices]
        b_samples = b_region_points[b_sample_indices]
        
        # 获取对应的法向量
        a_region_indices = np.where(a_region_mask)[0]
        b_region_indices = np.where(b_region_mask)[0]
        a_sample_normals = a_normals[a_region_indices[a_sample_indices]]
        b_sample_normals = b_normals[b_region_indices[b_sample_indices]]
        
        # 快速距离计算
        thicknesses = []
        
        # A点到B点的距离（简化版本）
        for i in range(len(a_samples)):
            # 找最近的B点（使用简化的距离计算）
            distances = np.sum((b_region_points - a_samples[i])**2, axis=1)
            closest_idx = np.argmin(distances)
            closest_b = b_region_points[closest_idx]
            
            # 计算垂直距离
            diff = a_samples[i] - closest_b
            thickness = abs(np.dot(diff, a_sample_normals[i]))
            thicknesses.append(thickness)
        
        # B点到A点的距离
        for i in range(len(b_samples)):
            distances = np.sum((a_region_points - b_samples[i])**2, axis=1)
            closest_idx = np.argmin(distances)
            closest_a = a_region_points[closest_idx]
            
            diff = b_samples[i] - closest_a
            thickness = abs(np.dot(diff, b_sample_normals[i]))
            thicknesses.append(thickness)
        
        if len(thicknesses) >= 4:
            thicknesses = np.array(thicknesses)
            # 简单的异常值过滤
            mean_thickness = np.mean(thicknesses)
            std_thickness = np.std(thicknesses)
            valid_mask = np.abs(thicknesses - mean_thickness) <= 2 * std_thickness
            valid_thicknesses = thicknesses[valid_mask]
            
            if len(valid_thicknesses) > 0:
                region_thickness = np.mean(valid_thicknesses)
                return {
                    "Region": region_idx,
                    "Thickness": region_thickness,
                    "x_start": x_start,
                    "x_end": x_end,
                    "y_start": y_start,
                    "y_end": y_end
                }
    except Exception as e:
        print(f"区域 {region_idx} 处理出错: {e}")
        return None

def create_continuous_colormap():
    """创建连续颜色映射"""
    return cm.get_cmap('viridis')

def get_color_for_thickness(thickness, cmap):
    """根据厚度值获取连续颜色"""
    fixed_min, fixed_max = 0.0, 1.0
    norm_thickness = (thickness - fixed_min) / (fixed_max - fixed_min) if fixed_max != fixed_min else 0.5
    norm_thickness = np.clip(norm_thickness, 0, 1)
    rgba = cmap(norm_thickness)
    return rgba[:3]

# ========== 超快速主处理函数 ==========
def measure_and_color_thickness_super_fast(file_path, region_size=120.0, num_samples=5):
    """超快速版本的厚度测量和着色函数"""
    start_time = time.time()
    
    # 1. 智能加载点云
    pcd, points, colors = load_point_cloud_smart(file_path)
    
    # 2. 快速颜色分类
    yellow_indices, blue_indices = get_color_masks_fast(colors)
    
    if len(yellow_indices) == 0 or len(blue_indices) == 0:
        print("未找到有效的A类或B类点")
        return pd.DataFrame(), pcd, 0.0, 1.0, None
    
    a_points = points[yellow_indices].copy()
    b_points = points[blue_indices].copy()
    
    print(f"A类点数: {len(a_points)}, B类点数: {len(b_points)}")
    
    # 3. 高效法向量计算
    a_normals = compute_normals_efficient(a_points)
    b_normals = compute_normals_efficient(b_points)
    
    # 4. 大区域划分（减少计算量）
    x_min, x_max = points[:, 0].min(), points[:, 0].max()
    y_min, y_max = points[:, 1].min(), points[:, 1].max()
    x_range = x_max - x_min
    y_range = y_max - y_min
    
    # 大幅减少区域数量
    total_area = x_range * y_range
    ideal_area_per_region = 200000  # 更大的区域
    target_regions = max(4, min(36, round(total_area / ideal_area_per_region)))  # 最多36个区域
    
    num_x = max(2, min(6, round(np.sqrt(target_regions * x_range / y_range))))
    num_y = max(2, min(6, round(target_regions / num_x)))
    
    x_step = x_range / num_x
    y_step = y_range / num_y
    
    print(f"超快速区域划分: {num_x}x{num_y} = {num_x * num_y} 个区域")
    print(f"每个区域大小: {x_step:.2f} x {y_step:.2f} mm")
    
    # 5. 准备多线程参数
    region_args = []
    region_idx = 0
    for i in range(num_x):
        for j in range(num_y):
            region_idx += 1
            x_start = x_min + i * x_step
            x_end = x_start + x_step
            y_start = y_min + j * y_step
            y_end = y_start + y_step
            
            region_args.append((
                region_idx, x_start, x_end, y_start, y_end,
                a_points, a_normals, b_points, b_normals, num_samples
            ))
    
    # 6. 多线程处理
    print(f"开始多线程处理 {len(region_args)} 个区域...")
    process_start = time.time()
    
    max_workers = min(6, len(region_args))  # 限制线程数
    print(f"使用 {max_workers} 个线程")
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        results = list(executor.map(process_region_super_fast, region_args))
    
    # 过滤有效结果
    valid_results = [r for r in results if r is not None]
    print(f"多线程处理耗时: {time.time() - process_start:.2f} 秒")
    print(f"成功处理 {len(valid_results)} 个区域")
    
    if not valid_results:
        print("没有有效的区域结果")
        return pd.DataFrame(), pcd, 0.0, 1.0, None
    
    # 7. 快速点云着色
    print("开始快速点云着色...")
    color_start = time.time()
    
    df = pd.DataFrame(valid_results)
    cmap = create_continuous_colormap()
    new_colors = colors.copy()
    
    # 批量处理颜色更新
    for result in valid_results:
        x_start, x_end = result['x_start'], result['x_end']
        y_start, y_end = result['y_start'], result['y_end']
        thickness = result['Thickness']
        
        # 区域掩码
        region_mask = ((points[:, 0] >= x_start) & (points[:, 0] <= x_end) & 
                      (points[:, 1] >= y_start) & (points[:, 1] <= y_end))
        
        # 只对黄色和蓝色点着色
        yellow_mask = np.isin(np.arange(len(points)), yellow_indices)
        blue_mask = np.isin(np.arange(len(points)), blue_indices)
        color_mask = region_mask & (yellow_mask | blue_mask)
        
        if np.any(color_mask):
            new_colors[color_mask] = get_color_for_thickness(thickness, cmap)
    
    # 应用新颜色
    pcd.colors = o3d.utility.Vector3dVector(new_colors)
    
    print(f"点云着色耗时: {time.time() - color_start:.2f} 秒")
    print(f"总处理耗时: {time.time() - start_time:.2f} 秒")
    
    # 清理内存
    del a_points, b_points, a_normals, b_normals, yellow_indices, blue_indices
    gc.collect()
    
    return df, pcd, 0.0, 1.0, cmap

# ========== 简化的显示界面 ==========
def display_table_fast(df, pcd, min_thickness, max_thickness, cmap):
    """快速显示界面"""
    root = tk.Tk()
    root.title("厚度测量结果 - 超快速版本")
    root.geometry("700x500")

    try:
        root.option_add("*Font", "SimHei 10")
    except:
        root.option_add("*Font", "Arial 10")

    def on_close():
        root.quit()
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_close)

    if df.empty:
        messagebox.showwarning("警告", "无有效厚度数据")
        root.destroy()
        return

    main_frame = ttk.Frame(root)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    title_label = ttk.Label(main_frame, text="区域厚度测量结果 (超快速版本)", font=("SimHei", 12, "bold"))
    title_label.pack(side=tk.TOP, pady=5)

    # 创建表格
    tree_frame = ttk.Frame(main_frame)
    tree_frame.pack(fill=tk.BOTH, expand=True, pady=5)

    tree = ttk.Treeview(tree_frame, columns=("Region", "Thickness"), show="headings", height=12)
    tree.heading("Region", text="区域")
    tree.heading("Thickness", text="厚度(mm)")
    tree.column("Region", width=100, anchor="center")
    tree.column("Thickness", width=120, anchor="center")

    scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=tree.yview)
    tree.configure(yscrollcommand=scrollbar.set)

    for _, row in df.iterrows():
        values = [f"区域{int(row['Region'])}", f"{row['Thickness']:.4f}"]
        tree.insert("", "end", values=values)

    tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    # 按钮区域
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=5)

    def show_point_cloud():
        try:
            vis = o3d.visualization.Visualizer()
            vis.create_window(window_name="厚度着色点云 (超快速版本)", width=800, height=600)
            vis.add_geometry(pcd)
            render_option = vis.get_render_option()
            render_option.point_size = 1.5
            vis.run()
            vis.destroy_window()
        except Exception as e:
            messagebox.showerror("错误", f"无法显示点云: {e}")

    def save_point_cloud():
        file_path = filedialog.asksaveasfilename(
            defaultextension=".ply",
            filetypes=[("PLY files", "*.ply"), ("PCD files", "*.pcd")]
        )
        if file_path:
            o3d.io.write_point_cloud(file_path, pcd)
            messagebox.showinfo("保存成功", f"点云已保存至: {file_path}")

    # 统计信息
    stats_text = f"处理区域: {len(df)} | 平均厚度: {df['Thickness'].mean():.4f}mm"
    stats_label = ttk.Label(button_frame, text=stats_text)
    stats_label.pack(side=tk.LEFT, padx=5)

    view_button = ttk.Button(button_frame, text="查看3D点云", command=show_point_cloud)
    view_button.pack(side=tk.RIGHT, padx=5)

    save_button = ttk.Button(button_frame, text="保存点云", command=save_point_cloud)
    save_button.pack(side=tk.RIGHT, padx=5)

    root.mainloop()

def select_file():
    """选择点云文件"""
    root = tk.Tk()
    root.withdraw()
    return filedialog.askopenfilename(filetypes=[("Point Cloud Files", "*.ply *.pcd")])

# ========== 超快速版本主函数 ==========
def main():
    """超快速版本主函数"""
    print("=== 点云厚度测量程序 - 超快速版本 ===")
    print("主要特点:")
    print("1. 智能采样 - 根据文件大小自动调整")
    print("2. 向量化计算 - 无需复杂JIT编译")
    print("3. 大区域处理 - 减少计算量")
    print("4. 多线程优化 - 平衡速度和内存")
    print("5. 简化算法 - 保持精度的同时提高速度")
    print("目标: 800MB文件在5分钟内完成")
    print("=====================================")

    file_path = select_file()
    if not file_path:
        print("未选择文件")
        return

    # 根据文件大小自动调整参数
    file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
    print(f"文件大小: {file_size_mb:.1f} MB")

    if file_size_mb > 600:
        region_size = 150.0
        num_samples = 4
        print("超大文件模式")
    elif file_size_mb > 300:
        region_size = 120.0
        num_samples = 5
        print("大文件模式")
    else:
        region_size = 100.0
        num_samples = 6
        print("标准模式")

    df, pcd, min_thickness, max_thickness, cmap = measure_and_color_thickness_super_fast(
        file_path, region_size=region_size, num_samples=num_samples
    )

    if df.empty:
        messagebox.showwarning("警告", "无法计算出有效厚度数据")
        return

    display_table_fast(df, pcd, min_thickness, max_thickness, cmap)

if __name__ == "__main__":
    main()
