import open3d as o3d
import numpy as np
import pandas as pd
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import matplotlib
from matplotlib import cm
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import time
from multiprocessing import Pool, cpu_count
from functools import partial
import gc
from scipy.spatial import cKDTree
from numba import jit, prange
import warnings
warnings.filterwarnings('ignore')

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# ========== 优化的工具函数 ==========
def load_point_cloud_optimized(file_path):
    """优化的点云加载函数"""
    start_time = time.time()
    print(f"开始加载点云文件: {file_path}")
    
    # 使用更高效的加载方式
    pcd = o3d.io.read_point_cloud(file_path)
    
    # 预先转换为numpy数组以提高后续访问速度
    points = np.asarray(pcd.points, dtype=np.float32)  # 使用float32减少内存
    colors = np.asarray(pcd.colors, dtype=np.float32) if pcd.has_colors() else np.ones((len(points), 3), dtype=np.float32) * 0.5
    
    print(f"加载点云耗时 {time.time() - start_time:.2f} 秒, 点数: {len(points)}")
    return pcd, points, colors

@jit(nopython=True, parallel=True)
def get_color_masks_optimized(colors):
    """使用Numba优化的颜色分类"""
    yellow_mask = np.zeros(len(colors), dtype=np.bool_)
    blue_mask = np.zeros(len(colors), dtype=np.bool_)
    
    for i in prange(len(colors)):
        # 黄色检测
        if (abs(colors[i, 0] - 1.0) < 0.1 and 
            abs(colors[i, 1] - 1.0) < 0.1 and 
            abs(colors[i, 2] - 0.0) < 0.1):
            yellow_mask[i] = True
        # 蓝色检测
        elif (abs(colors[i, 0] - 0.0) < 0.1 and 
              abs(colors[i, 1] - 0.0) < 0.1 and 
              abs(colors[i, 2] - 1.0) < 0.1):
            blue_mask[i] = True
    
    return yellow_mask, blue_mask

def compute_normals_optimized(points, knn=30):  # 减少knn以提高速度
    """优化的法向量计算"""
    start_time = time.time()
    
    # 创建临时点云对象
    temp_pcd = o3d.geometry.PointCloud()
    temp_pcd.points = o3d.utility.Vector3dVector(points)
    
    # 使用更快的法向量估计参数
    temp_pcd.estimate_normals(
        search_param=o3d.geometry.KDTreeSearchParamKNN(knn=knn),
        fast_normal_computation=True
    )
    
    normals = np.asarray(temp_pcd.normals, dtype=np.float32)
    print(f"计算法向量耗时 {time.time() - start_time:.2f} 秒")
    return normals

@jit(nopython=True)
def compute_perpendicular_distance_batch(points_a, points_b, normals):
    """批量计算垂直距离"""
    distances = np.zeros(len(points_a), dtype=np.float32)
    for i in range(len(points_a)):
        vector_ab = points_a[i] - points_b[i]
        distances[i] = abs(np.dot(vector_ab, normals[i]))
    return distances

def process_region_optimized(args):
    """优化的区域处理函数，用于多进程"""
    (region_idx, x_start, x_end, y_start, y_end, 
     a_points, a_normals, b_points, b_normals, 
     a_kdtree_data, b_kdtree_data, num_samples) = args
    
    try:
        # 重建KDTree（因为无法直接传递KDTree对象）
        a_kdtree = cKDTree(a_kdtree_data)
        b_kdtree = cKDTree(b_kdtree_data)
        
        # 区域内点的掩码
        a_region_mask = ((a_points[:, 0] >= x_start) & (a_points[:, 0] <= x_end) & 
                        (a_points[:, 1] >= y_start) & (a_points[:, 1] <= y_end))
        b_region_mask = ((b_points[:, 0] >= x_start) & (b_points[:, 0] <= x_end) & 
                        (b_points[:, 1] >= y_start) & (b_points[:, 1] <= y_end))
        
        a_region_points = a_points[a_region_mask]
        b_region_points = b_points[b_region_mask]
        
        if len(a_region_points) < num_samples or len(b_region_points) < num_samples:
            return None
        
        # 使用区域中心进行采样
        region_center = np.mean(a_region_points, axis=0)
        
        # 使用cKDTree进行更快的搜索
        _, a_sample_indices = a_kdtree.query(region_center, k=min(num_samples, len(a_points)))
        _, b_sample_indices = b_kdtree.query(region_center, k=min(num_samples, len(b_points)))
        
        a_samples = a_points[a_sample_indices]
        a_sample_normals = a_normals[a_sample_indices]
        b_samples = b_points[b_sample_indices]
        b_sample_normals = b_normals[b_sample_indices]
        
        # 批量计算厚度
        thicknesses = []
        
        # A点到B点的距离
        _, closest_b_indices = b_kdtree.query(a_samples, k=1)
        closest_b_points = b_points[closest_b_indices]
        a_thicknesses = compute_perpendicular_distance_batch(a_samples, closest_b_points, a_sample_normals)
        thicknesses.extend(a_thicknesses)
        
        # B点到A点的距离
        _, closest_a_indices = a_kdtree.query(b_samples, k=1)
        closest_a_points = a_points[closest_a_indices]
        b_thicknesses = compute_perpendicular_distance_batch(b_samples, closest_a_points, b_sample_normals)
        thicknesses.extend(b_thicknesses)
        
        if len(thicknesses) >= 6:
            thicknesses = np.sort(thicknesses)
            thicknesses = thicknesses[2:-2]  # 去除异常值
            region_thickness = np.mean(thicknesses)
            
            return {
                "Region": region_idx,
                "Thickness": region_thickness,
                "x_start": x_start,
                "x_end": x_end,
                "y_start": y_start,
                "y_end": y_end
            }
    except Exception as e:
        print(f"区域 {region_idx} 处理出错: {e}")
        return None

@jit(nopython=True, parallel=True)
def smooth_thickness_optimized(point_thickness, points, region_indices, region_thickness_map, region_size):
    """优化的厚度平滑函数"""
    smoothed_thickness = point_thickness.copy()
    
    for i in prange(len(points)):
        if not np.isnan(point_thickness[i]):
            # 简化的邻域平滑：使用区域内的平均值
            current_region = region_indices[i]
            if current_region >= 0:
                smoothed_thickness[i] = region_thickness_map[current_region]
    
    return smoothed_thickness

def create_continuous_colormap():
    """创建连续颜色映射"""
    return cm.get_cmap('viridis')

def get_color_for_thickness(thickness, cmap):
    """根据厚度值获取连续颜色，固定范围 [0, 1] mm"""
    fixed_min, fixed_max = 0.0, 1.0
    norm_thickness = (thickness - fixed_min) / (fixed_max - fixed_min) if fixed_max != fixed_min else 0.5
    norm_thickness = np.clip(norm_thickness, 0, 1)
    rgba = cmap(norm_thickness)
    return rgba[:3]

# ========== 优化后的主要处理函数 ==========
def measure_and_color_thickness_optimized(file_path, region_size=70.0, num_samples=8):
    """优化版本的厚度测量和着色函数"""
    start_time = time.time()

    # 1. 优化的点云加载
    pcd, points, colors = load_point_cloud_optimized(file_path)

    # 2. 使用Numba优化的颜色分类
    yellow_mask, blue_mask = get_color_masks_optimized(colors)
    a_points = points[yellow_mask].copy()
    b_points = points[blue_mask].copy()

    if len(a_points) == 0 or len(b_points) == 0:
        print("未找到有效的A类或B类点")
        return pd.DataFrame(), pcd, 0.0, 1.0, None

    print(f"A类点数: {len(a_points)}, B类点数: {len(b_points)}")

    # 3. 优化的法向量计算
    a_normals = compute_normals_optimized(a_points, knn=30)
    b_normals = compute_normals_optimized(b_points, knn=30)

    # 4. 构建优化的KDTree
    print("构建KDTree...")
    kdtree_start = time.time()
    a_kdtree = cKDTree(a_points)
    b_kdtree = cKDTree(b_points)
    print(f"KDTree构建耗时: {time.time() - kdtree_start:.2f} 秒")

    # 5. 优化的区域划分
    x_min, x_max = points[:, 0].min(), points[:, 0].max()
    y_min, y_max = points[:, 1].min(), points[:, 1].max()
    x_range = x_max - x_min
    y_range = y_max - y_min

    # 动态调整区域数量以平衡精度和性能
    total_area = x_range * y_range
    ideal_area_per_region = 80000  # 增大区域面积以减少区域数量
    target_regions = max(4, min(100, round(total_area / ideal_area_per_region)))  # 限制区域数量

    num_x = max(2, min(10, round(np.sqrt(target_regions * x_range / y_range))))
    num_y = max(2, min(10, round(target_regions / num_x)))

    x_step = x_range / num_x
    y_step = y_range / num_y

    print(f"优化后区域划分: {num_x}x{num_y} = {num_x * num_y} 个区域")
    print(f"每个区域大小: {x_step:.2f} x {y_step:.2f} mm")

    # 6. 准备多进程参数
    region_args = []
    region_idx = 0
    for i in range(num_x):
        for j in range(num_y):
            region_idx += 1
            x_start = x_min + i * x_step
            x_end = x_start + x_step
            y_start = y_min + j * y_step
            y_end = y_start + y_step

            region_args.append((
                region_idx, x_start, x_end, y_start, y_end,
                a_points, a_normals, b_points, b_normals,
                a_points, b_points, num_samples  # KDTree数据
            ))

    # 7. 多进程处理区域
    print(f"开始多进程处理 {len(region_args)} 个区域...")
    process_start = time.time()

    # 使用CPU核心数的75%以避免系统过载
    num_processes = max(1, int(cpu_count() * 0.75))
    print(f"使用 {num_processes} 个进程")

    with Pool(processes=num_processes) as pool:
        results = pool.map(process_region_optimized, region_args)

    # 过滤有效结果
    valid_results = [r for r in results if r is not None]
    print(f"多进程处理耗时: {time.time() - process_start:.2f} 秒")
    print(f"成功处理 {len(valid_results)} 个区域")

    if not valid_results:
        print("没有有效的区域结果")
        return pd.DataFrame(), pcd, 0.0, 1.0, None

    # 8. 优化的点云着色
    print("开始点云着色...")
    color_start = time.time()

    df = pd.DataFrame(valid_results)
    cmap = create_continuous_colormap()
    new_colors = colors.copy()

    # 创建区域索引映射以优化平滑处理
    region_indices = np.full(len(points), -1, dtype=np.int32)
    region_thickness_map = np.zeros(len(valid_results) + 1, dtype=np.float32)

    for idx, result in enumerate(valid_results):
        x_start, x_end = result['x_start'], result['x_end']
        y_start, y_end = result['y_start'], result['y_end']
        thickness = result['Thickness']

        # 区域掩码
        region_mask = ((points[:, 0] >= x_start) & (points[:, 0] <= x_end) &
                      (points[:, 1] >= y_start) & (points[:, 1] <= y_end))
        color_mask = region_mask & (yellow_mask | blue_mask)

        # 设置颜色
        if np.any(color_mask):
            new_colors[color_mask] = get_color_for_thickness(thickness, cmap)
            region_indices[color_mask] = idx
            region_thickness_map[idx] = thickness

    # 应用新颜色
    pcd.colors = o3d.utility.Vector3dVector(new_colors)

    print(f"点云着色耗时: {time.time() - color_start:.2f} 秒")
    print(f"总处理耗时: {time.time() - start_time:.2f} 秒")

    # 强制垃圾回收以释放内存
    gc.collect()

    return df, pcd, 0.0, 1.0, cmap

# ========== 保持原有的显示界面不变 ==========
def display_table(df, pcd, min_thickness, max_thickness, cmap):
    """显示厚度测量结果和连续颜色映射（固定厚度范围 [0, 1] mm）"""
    start_time = time.time()
    root = tk.Tk()
    root.title("厚度测量结果 - 优化版本")
    root.geometry("1000x800")

    try:
        root.option_add("*Font", "SimHei 12")
    except:
        root.option_add("*Font", "Arial 12")

    def on_close():
        root.quit()
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_close)

    if df.empty:
        messagebox.showwarning("警告", "无有效厚度数据")
        root.destroy()
        return

    main_frame = ttk.Frame(root)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    left_frame = ttk.Frame(main_frame)
    left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

    title_label = ttk.Label(left_frame, text="区域厚度测量结果 (优化版本)", font=("SimHei", 14, "bold"))
    title_label.pack(side=tk.TOP, pady=5)

    tree = ttk.Treeview(left_frame, columns=("Region", "Thickness"), show="headings",
                        height=min(len(df), 20))
    tree.heading("Region", text="区域索引")
    tree.heading("Thickness", text="平均厚度 (mm)")
    tree.column("Region", width=150, anchor="center")
    tree.column("Thickness", width=150, anchor="center")

    vsb = ttk.Scrollbar(left_frame, orient="vertical", command=tree.yview)
    tree.configure(yscrollcommand=vsb.set)

    for _, row in df.iterrows():
        values = [f"区域 {int(row['Region'])}", f"{row['Thickness']:.4f}"]
        tree.insert("", "end", values=values, tags=(int(row['Region']),))

    tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    vsb.pack(side=tk.RIGHT, fill=tk.Y)

    right_frame = ttk.Frame(main_frame)
    right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)

    fig = Figure(figsize=(6, 8), dpi=100)
    ax1 = fig.add_subplot(211)
    ax2 = fig.add_subplot(212)

    ax1.plot(df["Region"], df["Thickness"], '-o', color='darkblue', markersize=8, linewidth=2)
    ax1.set_title("各区域平均厚度分布 (优化版本)", fontsize=14)
    ax1.set_xlabel("区域索引", fontsize=12)
    ax1.set_ylabel("平均厚度 (mm)", fontsize=12)
    ax1.grid(True, linestyle='--', alpha=0.7)
    ax1.set_xticks(df["Region"])
    ax1.set_xticklabels([f"区域 {int(i)}" for i in df["Region"]], rotation=45)

    ax2.set_title('厚度-颜色映射关系 (固定范围 0-1 mm)', fontsize=14)
    ax2.axis('off')

    norm = plt.Normalize(0.0, 1.0)
    sm = plt.cm.ScalarMappable(cmap=cmap, norm=norm)
    cbar = fig.colorbar(sm, ax=ax2, orientation='horizontal', pad=0.2, aspect=30)
    cbar.set_label('厚度 (mm)', fontsize=12)
    cbar.ax.tick_params(labelsize=10)

    ax2.text(0.5, 0.9, "厚度-颜色对应关系 (0-1 mm)", ha='center', va='center', fontsize=12, fontweight='bold')

    canvas = FigureCanvasTkAgg(fig, master=right_frame)
    canvas.draw()
    canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    button_frame = ttk.Frame(root)
    button_frame.pack(fill=tk.X, padx=10, pady=10)

    def show_point_cloud():
        try:
            vis = o3d.visualization.Visualizer()
            vis.create_window(window_name=f"厚度着色点云 (优化版本, 固定厚度范围: 0-1 mm)", width=800, height=600)
            vis.add_geometry(pcd)
            render_option = vis.get_render_option()
            render_option.point_size = 2.0
            vis.add_geometry(o3d.geometry.TriangleMesh.create_coordinate_frame(size=10.0))
            vis.run()
            vis.destroy_window()
        except Exception as e:
            print(f"显示点云出错: {e}")
            messagebox.showerror("错误", f"无法显示点云: {e}")

    def show_region_point_cloud(event):
        start_time = time.time()
        selection = tree.selection()
        if not selection:
            return

        item = tree.item(selection[0])
        region_id = int(item['tags'][0])
        region_data = df[df['Region'] == region_id].iloc[0]
        x_start = region_data['x_start']
        x_end = region_data['x_end']
        y_start = region_data['y_start']
        y_end = region_data['y_end']
        thickness = region_data['Thickness']

        points = np.asarray(pcd.points)
        colors = np.asarray(pcd.colors)
        region_mask = (points[:, 0] >= x_start) & (points[:, 0] <= x_end) & \
                      (points[:, 1] >= y_start) & (points[:, 1] <= y_end)

        if np.sum(region_mask) == 0:
            messagebox.showwarning("警告", f"区域 {region_id} 无点云数据")
            return

        region_pcd = o3d.geometry.PointCloud()
        region_pcd.points = o3d.utility.Vector3dVector(points)
        new_colors = colors.copy()
        new_colors[region_mask] = [1, 0, 0]
        region_pcd.colors = o3d.utility.Vector3dVector(new_colors)

        z_min, z_max = points[:, 2].min(), points[:, 2].max()
        bbox = o3d.geometry.AxisAlignedBoundingBox(
            min_bound=[x_start, y_start, z_min],
            max_bound=[x_end, y_end, z_max]
        )
        bbox.color = (1, 0, 0)

        try:
            vis = o3d.visualization.Visualizer()
            vis.create_window(window_name=f"区域 {region_id} 点云 (厚度: {thickness:.4f}mm)", width=800, height=600)
            vis.add_geometry(region_pcd)
            vis.add_geometry(bbox)
            render_option = vis.get_render_option()
            render_option.point_size = 2.0
            vis.add_geometry(o3d.geometry.TriangleMesh.create_coordinate_frame(size=10.0))
            vis.run()
            vis.destroy_window()
            print(f"区域 {region_id} 可视化耗时 {time.time() - start_time:.2f} 秒")
        except Exception as e:
            print(f"显示区域点云出错: {e}")
            messagebox.showerror("错误", f"无法显示区域点云: {e}")

    tree.bind('<Double-1>', show_region_point_cloud)

    view_button = ttk.Button(button_frame, text="查看3D点云", command=show_point_cloud)
    view_button.pack(side=tk.RIGHT, padx=10, pady=5, ipadx=10, ipady=5)

    def save_point_cloud():
        file_path = filedialog.asksaveasfilename(
            defaultextension=".ply",
            filetypes=[("PLY files", "*.ply"), ("PCD files", "*.pcd")]
        )
        if file_path:
            o3d.io.write_point_cloud(file_path, pcd)
            messagebox.showinfo("保存成功", f"点云已保存至: {file_path}")

    save_button = ttk.Button(button_frame, text="保存点云", command=save_point_cloud)
    save_button.pack(side=tk.RIGHT, padx=10, pady=5, ipadx=10, ipady=5)

    print(f"显示表格设置耗时 {time.time() - start_time:.2f} 秒")
    root.mainloop()

def select_file():
    """选择点云文件"""
    root = tk.Tk()
    root.withdraw()
    return filedialog.askopenfilename(filetypes=[("Point Cloud Files", "*.ply *.pcd")])

# ========== 优化版本的主函数 ==========
def main():
    """优化版本的主函数"""
    print("=== 点云厚度测量程序 - 优化版本 ===")
    print("主要优化:")
    print("1. 多进程并行处理区域")
    print("2. 使用Numba加速数值计算")
    print("3. 优化的KDTree搜索")
    print("4. 减少内存使用和重复计算")
    print("5. 动态调整区域数量")
    print("=====================================")

    file_path = select_file()
    if not file_path:
        print("未选择文件")
        return

    df, pcd, min_thickness, max_thickness, cmap = measure_and_color_thickness_optimized(
        file_path, region_size=70.0, num_samples=8
    )

    if df.empty:
        messagebox.showwarning("警告", "无法计算出有效厚度数据")
        return

    display_table(df, pcd, min_thickness, max_thickness, cmap)

if __name__ == "__main__":
    main()
