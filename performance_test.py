"""
性能测试脚本 - 对比原版本和优化版本的性能
"""
import time
import os
import sys
import psutil
import gc
from tkinter import filedialog
import tkinter as tk

def get_memory_usage():
    """获取当前内存使用量（MB）"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024

def test_original_version(file_path):
    """测试原版本性能"""
    print("\n=== 测试原版本 ===")
    start_time = time.time()
    start_memory = get_memory_usage()
    
    try:
        # 导入原版本函数
        from main import measure_and_color_thickness
        
        # 运行原版本
        df, pcd, min_thickness, max_thickness, cmap = measure_and_color_thickness(
            file_path, region_size=70.0, num_samples=10
        )
        
        end_time = time.time()
        end_memory = get_memory_usage()
        
        print(f"原版本结果:")
        print(f"  处理时间: {end_time - start_time:.2f} 秒")
        print(f"  内存使用: {end_memory - start_memory:.2f} MB")
        print(f"  处理区域数: {len(df) if not df.empty else 0}")
        
        # 清理内存
        del df, pcd, min_thickness, max_thickness, cmap
        gc.collect()
        
        return end_time - start_time, end_memory - start_memory, len(df) if 'df' in locals() and not df.empty else 0
        
    except Exception as e:
        print(f"原版本测试失败: {e}")
        return None, None, 0

def test_optimized_version(file_path):
    """测试优化版本性能"""
    print("\n=== 测试优化版本 ===")
    start_time = time.time()
    start_memory = get_memory_usage()
    
    try:
        # 导入优化版本函数
        from main_optimized import measure_and_color_thickness_optimized
        
        # 运行优化版本
        df, pcd, min_thickness, max_thickness, cmap = measure_and_color_thickness_optimized(
            file_path, region_size=70.0, num_samples=8
        )
        
        end_time = time.time()
        end_memory = get_memory_usage()
        
        print(f"优化版本结果:")
        print(f"  处理时间: {end_time - start_time:.2f} 秒")
        print(f"  内存使用: {end_memory - start_memory:.2f} MB")
        print(f"  处理区域数: {len(df) if not df.empty else 0}")
        
        # 清理内存
        del df, pcd, min_thickness, max_thickness, cmap
        gc.collect()
        
        return end_time - start_time, end_memory - start_memory, len(df) if 'df' in locals() and not df.empty else 0
        
    except Exception as e:
        print(f"优化版本测试失败: {e}")
        return None, None, 0

def get_file_info(file_path):
    """获取文件信息"""
    file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
    print(f"\n文件信息:")
    print(f"  文件路径: {file_path}")
    print(f"  文件大小: {file_size:.2f} MB")
    return file_size

def select_file():
    """选择测试文件"""
    root = tk.Tk()
    root.withdraw()
    file_path = filedialog.askopenfilename(
        title="选择要测试的PLY文件",
        filetypes=[("Point Cloud Files", "*.ply *.pcd")]
    )
    root.destroy()
    return file_path

def main():
    """主测试函数"""
    print("=== 点云处理性能对比测试 ===")
    print("此脚本将对比原版本和优化版本的性能")
    print("请选择要测试的PLY文件...")
    
    file_path = select_file()
    if not file_path:
        print("未选择文件，退出测试")
        return
    
    # 获取文件信息
    file_size = get_file_info(file_path)
    
    # 获取系统信息
    print(f"\n系统信息:")
    print(f"  CPU核心数: {psutil.cpu_count()}")
    print(f"  可用内存: {psutil.virtual_memory().available / (1024**3):.2f} GB")
    
    # 测试原版本
    original_time, original_memory, original_regions = test_original_version(file_path)
    
    # 等待一段时间让系统稳定
    time.sleep(2)
    
    # 测试优化版本
    optimized_time, optimized_memory, optimized_regions = test_optimized_version(file_path)
    
    # 性能对比
    print("\n" + "="*50)
    print("性能对比结果:")
    print("="*50)
    
    if original_time and optimized_time:
        speedup = original_time / optimized_time
        print(f"处理时间对比:")
        print(f"  原版本:   {original_time:.2f} 秒")
        print(f"  优化版本: {optimized_time:.2f} 秒")
        print(f"  加速比:   {speedup:.2f}x")
        
        if original_memory and optimized_memory:
            memory_ratio = optimized_memory / original_memory if original_memory > 0 else 1
            print(f"\n内存使用对比:")
            print(f"  原版本:   {original_memory:.2f} MB")
            print(f"  优化版本: {optimized_memory:.2f} MB")
            print(f"  内存比例: {memory_ratio:.2f}x")
        
        print(f"\n区域处理对比:")
        print(f"  原版本:   {original_regions} 个区域")
        print(f"  优化版本: {optimized_regions} 个区域")
        
        # 预估800MB文件的处理时间
        if file_size > 0:
            scale_factor = 800 / file_size
            estimated_original = original_time * scale_factor
            estimated_optimized = optimized_time * scale_factor
            
            print(f"\n800MB文件预估处理时间:")
            print(f"  原版本预估:   {estimated_original/60:.1f} 分钟")
            print(f"  优化版本预估: {estimated_optimized/60:.1f} 分钟")
            
            if estimated_optimized <= 600:  # 10分钟 = 600秒
                print(f"  ✓ 优化版本预计可在10分钟内完成800MB文件处理")
            else:
                print(f"  ⚠ 优化版本预计需要 {estimated_optimized/60:.1f} 分钟处理800MB文件")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
